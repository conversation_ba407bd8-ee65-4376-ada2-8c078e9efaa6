// 🚀 Elite Trading Bot - BiBot Main Application
import { useState, useEffect } from 'react'
import { Card } from './renderer/components/ui/Card'
import { Button } from './renderer/components/ui/Button'
import { Input } from './renderer/components/ui/Input'
import { Select, SelectOption } from './renderer/components/ui/Select'
import {
	TrendingUp,
	TrendingDown,
	Activity,
	DollarSign,
	Target,
	Shield,
	Zap,
	Settings,
	Play,
	Square,
	Wifi,
	WifiOff
} from 'lucide-react'

// Mock data for demonstration
const mockAssets: SelectOption[] = [
	{ value: 'EURUSD', label: 'EUR/USD', icon: <DollarSign className="h-4 w-4" /> },
	{ value: 'GBPUSD', label: 'GBP/USD', icon: <DollarSign className="h-4 w-4" /> },
	{ value: 'BTCUSD', label: 'BTC/USD', icon: <Activity className="h-4 w-4" /> },
	{ value: 'ETHUSD', label: 'ETH/USD', icon: <Activity className="h-4 w-4" /> }
]

const tradeDurations: SelectOption[] = [
	{ value: 'S5', label: '5 Seconds' },
	{ value: 'S15', label: '15 Seconds' },
	{ value: 'S30', label: '30 Seconds' },
	{ value: 'M1', label: '1 Minute' },
	{ value: 'M5', label: '5 Minutes' }
]

const martingaleModes: SelectOption[] = [
	{ value: 'none', label: 'None' },
	{ value: 'martingale', label: 'Martingale' },
	{ value: 'anti-martingale', label: 'Anti-Martingale' },
	{ value: 'fibonacci', label: 'Fibonacci' }
]

function App() {
	const [isConnected, setIsConnected] = useState(false)
	const [isTrading, setIsTrading] = useState(false)
	const [selectedAsset, setSelectedAsset] = useState('EURUSD')
	const [tradeAmount, setTradeAmount] = useState('10')
	const [tradeDuration, setTradeDuration] = useState('M1')
	const [martingaleMode, setMartingaleMode] = useState('none')

	// Mock performance data
	const [performanceData, setPerformanceData] = useState({
		balance: 1250.0,
		profitLoss: 150.0,
		winRate: 72.5,
		totalTrades: 47,
		activeTrades: 3
	})

	useEffect(() => {
		// Simulate real-time updates
		const interval = setInterval(() => {
			setPerformanceData(prev => ({
				...prev,
				balance: prev.balance + (Math.random() - 0.5) * 10,
				profitLoss: prev.profitLoss + (Math.random() - 0.5) * 5
			}))
		}, 2000)

		return () => clearInterval(interval)
	}, [])

	const handleConnect = () => {
		setIsConnected(!isConnected)
	}

	const handleStartTrading = () => {
		setIsTrading(!isTrading)
	}

	const handleBuyTrade = () => {
		console.log('🟢 BUY Trade executed:', {
			asset: selectedAsset,
			amount: tradeAmount,
			duration: tradeDuration
		})
	}

	const handleSellTrade = () => {
		console.log('🔴 SELL Trade executed:', {
			asset: selectedAsset,
			amount: tradeAmount,
			duration: tradeDuration
		})
	}

	return (
		<div className="h-screen w-screen bg-[var(--color-background)] text-[var(--color-text-primary)] flex flex-col">
			{/* Header */}
			<header className="border-b border-[var(--color-border)] bg-[var(--color-surface)] px-6 py-4">
				<div className="flex items-center justify-between">
					<div className="flex items-center gap-4">
						<div className="flex items-center gap-2">
							<Zap className="h-8 w-8 text-[var(--color-primary)]" />
							<h1 className="text-2xl font-bold text-gradient">BiBot Elite</h1>
						</div>
						<div className="flex items-center gap-2 text-sm">
							{isConnected ? (
								<>
									<Wifi className="h-4 w-4 text-[var(--color-success)]" />
									<span className="text-[var(--color-success)]">Connected</span>
								</>
							) : (
								<>
									<WifiOff className="h-4 w-4 text-[var(--color-danger)]" />
									<span className="text-[var(--color-danger)]">Disconnected</span>
								</>
							)}
						</div>
					</div>

					<div className="flex items-center gap-4">
						<Button
							variant={isConnected ? 'danger' : 'primary'}
							onClick={handleConnect}
							icon={isConnected ? <WifiOff className="h-4 w-4" /> : <Wifi className="h-4 w-4" />}
						>
							{isConnected ? 'Disconnect' : 'Connect'}
						</Button>
						<Button variant="ghost" size="icon">
							<Settings className="h-4 w-4" />
						</Button>
					</div>
				</div>
			</header>

			{/* Main Content */}
			<div className="flex-1 flex overflow-hidden">
				{/* Left Sidebar - Trading Controls */}
				<div className="w-80 border-r border-[var(--color-border)] bg-[var(--color-surface)] p-4 overflow-y-auto">
					<div className="space-y-4">
						{/* Performance Dashboard */}
						<Card variant="trading" title="📊 Performance" icon={<Activity className="h-5 w-5" />}>
							<div className="grid grid-cols-2 gap-4 text-sm">
								<div>
									<div className="text-[var(--color-text-muted)]">Balance</div>
									<div className="text-lg font-semibold text-[var(--color-success)]">
										${performanceData.balance.toFixed(2)}
									</div>
								</div>
								<div>
									<div className="text-[var(--color-text-muted)]">P/L</div>
									<div
										className={`text-lg font-semibold ${
											performanceData.profitLoss >= 0 ? 'text-[var(--color-success)]' : 'text-[var(--color-danger)]'
										}`}
									>
										{performanceData.profitLoss >= 0 ? '+' : ''}${performanceData.profitLoss.toFixed(2)}
									</div>
								</div>
								<div>
									<div className="text-[var(--color-text-muted)]">Win Rate</div>
									<div className="text-lg font-semibold">{performanceData.winRate}%</div>
								</div>
								<div>
									<div className="text-[var(--color-text-muted)]">Trades</div>
									<div className="text-lg font-semibold">{performanceData.totalTrades}</div>
								</div>
							</div>
						</Card>

						{/* Trading Configuration */}
						<Card variant="trading" title="⚙️ Trading Config" icon={<Settings className="h-5 w-5" />}>
							<div className="space-y-3">
								<Select
									label="Asset"
									options={mockAssets}
									value={selectedAsset}
									onChange={setSelectedAsset}
									size="compact"
								/>

								<Input
									label="Amount ($)"
									type="number"
									value={tradeAmount}
									onChange={e => setTradeAmount(e.target.value)}
									size="compact"
									prefix="$"
								/>

								<Select
									label="Duration"
									options={tradeDurations}
									value={tradeDuration}
									onChange={setTradeDuration}
									size="compact"
								/>

								<Select
									label="Martingale"
									options={martingaleModes}
									value={martingaleMode}
									onChange={setMartingaleMode}
									size="compact"
								/>
							</div>
						</Card>

						{/* Trading Controls */}
						<Card variant="trading" title="🎮 Controls" icon={<Target className="h-5 w-5" />}>
							<div className="space-y-3">
								<Button
									variant={isTrading ? 'danger' : 'success'}
									className="w-full"
									onClick={handleStartTrading}
									icon={isTrading ? <Square className="h-4 w-4" /> : <Play className="h-4 w-4" />}
									disabled={!isConnected}
								>
									{isTrading ? 'STOP BOT' : 'START BOT'}
								</Button>

								<div className="grid grid-cols-2 gap-2">
									<Button
										variant="buy"
										onClick={handleBuyTrade}
										disabled={!isConnected || !isTrading}
										icon={<TrendingUp className="h-4 w-4" />}
									>
										BUY
									</Button>
									<Button
										variant="sell"
										onClick={handleSellTrade}
										disabled={!isConnected || !isTrading}
										icon={<TrendingDown className="h-4 w-4" />}
									>
										SELL
									</Button>
								</div>
							</div>
						</Card>

						{/* Risk Management */}
						<Card variant="warning" title="🛡️ Risk Management" icon={<Shield className="h-5 w-5" />}>
							<div className="space-y-2 text-sm">
								<div className="flex justify-between">
									<span className="text-[var(--color-text-muted)]">Daily Loss Limit:</span>
									<span className="text-[var(--color-warning)]">$500</span>
								</div>
								<div className="flex justify-between">
									<span className="text-[var(--color-text-muted)]">Max Drawdown:</span>
									<span className="text-[var(--color-warning)]">20%</span>
								</div>
								<div className="flex justify-between">
									<span className="text-[var(--color-text-muted)]">Emergency Stop:</span>
									<span className="text-[var(--color-success)]">Active</span>
								</div>
							</div>
						</Card>
					</div>
				</div>

				{/* Main Content Area */}
				<div className="flex-1 p-6 overflow-y-auto">
					<div className="max-w-6xl mx-auto space-y-6">
						{/* Welcome Message */}
						<Card variant="glass" size="lg">
							<div className="text-center">
								<h2 className="text-3xl font-bold text-gradient mb-4">🚀 Welcome to BiBot Elite Trading System</h2>
								<p className="text-[var(--color-text-secondary)] text-lg mb-6">
									Your ultimate high-performance scalping trading companion with advanced algorithmic strategies
								</p>
								<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
									<div className="text-center">
										<div className="text-2xl mb-2">⚡</div>
										<h3 className="font-semibold mb-1">Lightning Fast</h3>
										<p className="text-sm text-[var(--color-text-muted)]">Sub-100ms response times</p>
									</div>
									<div className="text-center">
										<div className="text-2xl mb-2">🧠</div>
										<h3 className="font-semibold mb-1">AI-Powered</h3>
										<p className="text-sm text-[var(--color-text-muted)]">8 advanced trading algorithms</p>
									</div>
									<div className="text-center">
										<div className="text-2xl mb-2">🛡️</div>
										<h3 className="font-semibold mb-1">Risk Protected</h3>
										<p className="text-sm text-[var(--color-text-muted)]">Comprehensive safety features</p>
									</div>
								</div>
							</div>
						</Card>

						{/* Status Cards */}
						<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
							<Card variant="success" hover="lift">
								<div className="flex items-center justify-between">
									<div>
										<p className="text-sm text-[var(--color-text-muted)]">Active Strategies</p>
										<p className="text-2xl font-bold">8</p>
									</div>
									<Activity className="h-8 w-8 text-[var(--color-success)]" />
								</div>
							</Card>

							<Card variant="default" hover="lift">
								<div className="flex items-center justify-between">
									<div>
										<p className="text-sm text-[var(--color-text-muted)]">Memory Usage</p>
										<p className="text-2xl font-bold">245MB</p>
									</div>
									<Zap className="h-8 w-8 text-[var(--color-info)]" />
								</div>
							</Card>

							<Card variant="default" hover="lift">
								<div className="flex items-center justify-between">
									<div>
										<p className="text-sm text-[var(--color-text-muted)]">Uptime</p>
										<p className="text-2xl font-bold">99.8%</p>
									</div>
									<Shield className="h-8 w-8 text-[var(--color-primary)]" />
								</div>
							</Card>

							<Card variant="default" hover="lift">
								<div className="flex items-center justify-between">
									<div>
										<p className="text-sm text-[var(--color-text-muted)]">Response Time</p>
										<p className="text-2xl font-bold">47ms</p>
									</div>
									<Target className="h-8 w-8 text-[var(--color-warning)]" />
								</div>
							</Card>
						</div>

						{/* Trading Algorithms */}
						<Card title="🧠 Active Trading Algorithms" variant="trading">
							<div className="grid grid-cols-2 md:grid-cols-4 gap-4">
								{[
									{ name: 'RSI', status: 'active', signal: 'buy' },
									{ name: 'MACD', status: 'active', signal: 'neutral' },
									{ name: 'Bollinger', status: 'active', signal: 'sell' },
									{ name: 'Stochastic', status: 'active', signal: 'buy' },
									{ name: 'Williams %R', status: 'active', signal: 'neutral' },
									{ name: 'ATR', status: 'active', signal: 'buy' },
									{ name: 'ORB', status: 'active', signal: 'sell' },
									{ name: 'Moving Avg', status: 'active', signal: 'buy' }
								].map(algo => (
									<div key={algo.name} className="text-center p-3 rounded border border-[var(--color-border)]">
										<div className="font-medium">{algo.name}</div>
										<div
											className={`text-sm ${
												algo.signal === 'buy'
													? 'text-[var(--color-success)]'
													: algo.signal === 'sell'
													? 'text-[var(--color-danger)]'
													: 'text-[var(--color-text-muted)]'
											}`}
										>
											{algo.signal.toUpperCase()}
										</div>
									</div>
								))}
							</div>
						</Card>
					</div>
				</div>
			</div>
		</div>
	)
}

export default App

// 🧪 Test Setup Configuration
import { vi } from 'vitest'

// Mock localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {}

  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value.toString()
    },
    removeItem: (key: string) => {
      delete store[key]
    },
    clear: () => {
      store = {}
    },
    length: Object.keys(store).length,
    key: (index: number) => Object.keys(store)[index] || null
  }
})()

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

// Mock sessionStorage
Object.defineProperty(window, 'sessionStorage', {
  value: localStorageMock
})

// Mock window.ipcRenderer for Electron
Object.defineProperty(window, 'ipcRenderer', {
  value: {
    on: vi.fn(),
    off: vi.fn(),
    invoke: vi.fn(),
    send: vi.fn(),
    removeAllListeners: vi.fn()
  }
})

// Mock performance.now for consistent timing in tests
Object.defineProperty(window, 'performance', {
  value: {
    now: vi.fn(() => Date.now())
  }
})

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: vi.fn(),
  debug: vi.fn(),
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn()
}

// Mock fetch for API calls
global.fetch = vi.fn()

// Mock WebSocket
global.WebSocket = vi.fn(() => ({
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  send: vi.fn(),
  close: vi.fn(),
  readyState: 1,
  CONNECTING: 0,
  OPEN: 1,
  CLOSING: 2,
  CLOSED: 3
}))

// Mock ResizeObserver
global.ResizeObserver = vi.fn(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn()
}))

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn()
}))

// Mock canvas context for chart tests
HTMLCanvasElement.prototype.getContext = vi.fn(() => ({
  fillRect: vi.fn(),
  clearRect: vi.fn(),
  getImageData: vi.fn(),
  putImageData: vi.fn(),
  createImageData: vi.fn(),
  setTransform: vi.fn(),
  drawImage: vi.fn(),
  save: vi.fn(),
  fillText: vi.fn(),
  restore: vi.fn(),
  beginPath: vi.fn(),
  moveTo: vi.fn(),
  lineTo: vi.fn(),
  closePath: vi.fn(),
  stroke: vi.fn(),
  fill: vi.fn(),
  scale: vi.fn(),
  rotate: vi.fn(),
  translate: vi.fn(),
  clip: vi.fn(),
  quadraticCurveTo: vi.fn(),
  bezierCurveTo: vi.fn(),
  arc: vi.fn(),
  arcTo: vi.fn(),
  ellipse: vi.fn(),
  rect: vi.fn(),
  roundRect: vi.fn(),
  createLinearGradient: vi.fn(() => ({
    addColorStop: vi.fn()
  })),
  createRadialGradient: vi.fn(() => ({
    addColorStop: vi.fn()
  })),
  setLineDash: vi.fn(),
  getLineDash: vi.fn(() => []),
  measureText: vi.fn(() => ({ width: 0 }))
}))

// Mock URL.createObjectURL and revokeObjectURL for file operations
global.URL.createObjectURL = vi.fn(() => 'mock-url')
global.URL.revokeObjectURL = vi.fn()

// Mock Blob for file operations
global.Blob = vi.fn(() => ({
  size: 0,
  type: 'application/json'
}))

// Mock File for file upload tests
global.File = vi.fn(() => ({
  name: 'test.json',
  size: 1024,
  type: 'application/json',
  text: vi.fn(() => Promise.resolve('{"test": true}'))
}))

// Mock crypto for ID generation
Object.defineProperty(global, 'crypto', {
  value: {
    getRandomValues: vi.fn((arr) => {
      for (let i = 0; i < arr.length; i++) {
        arr[i] = Math.floor(Math.random() * 256)
      }
      return arr
    }),
    randomUUID: vi.fn(() => 'mock-uuid-' + Math.random().toString(36).substr(2, 9))
  }
})

// Mock Date.now for consistent timestamps in tests
const originalDateNow = Date.now
Date.now = vi.fn(() => 1640995200000) // Fixed timestamp: 2022-01-01 00:00:00 UTC

// Restore Date.now after each test
afterEach(() => {
  Date.now = originalDateNow
})

// Clear all mocks after each test
afterEach(() => {
  vi.clearAllMocks()
  localStorageMock.clear()
})

// Global test utilities
export const createMockOHLC = (count: number = 100, basePrice: number = 1.0850) => {
  const data = []
  const now = Date.now()
  
  for (let i = 0; i < count; i++) {
    const timestamp = new Date(now - (count - i) * 60000) // 1 minute intervals
    const open = basePrice + (Math.random() - 0.5) * 0.01
    const close = open + (Math.random() - 0.5) * 0.005
    const high = Math.max(open, close) + Math.random() * 0.003
    const low = Math.min(open, close) - Math.random() * 0.003
    
    data.push({
      timestamp,
      open,
      high,
      low,
      close,
      volume: Math.floor(Math.random() * 1000) + 100
    })
  }
  
  return data
}

export const createMockTrade = (overrides = {}) => ({
  id: 'mock-trade-' + Math.random().toString(36).substr(2, 9),
  sessionId: 'mock-session-1',
  timestamp: new Date(),
  entryTime: new Date(),
  asset: 'EURUSD',
  action: 'BUY',
  amount: 100,
  result: 'win',
  profit: 80,
  strategy: 'Mock Strategy',
  ...overrides
})

export const createMockSession = (overrides = {}) => ({
  id: 'mock-session-' + Math.random().toString(36).substr(2, 9),
  startTime: new Date(),
  initialBalance: 10000,
  currentBalance: 10000,
  totalTrades: 0,
  winningTrades: 0,
  losingTrades: 0,
  winRate: 0,
  maxDrawdown: 0,
  isActive: true,
  ...overrides
})

export const waitFor = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

export const flushPromises = () => new Promise(resolve => setImmediate(resolve))

// 🎯 Elite Input Component
import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { clsx } from 'clsx';

const inputVariants = cva(
  'flex w-full rounded-md border bg-transparent text-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-[var(--color-text-muted)] focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
  {
    variants: {
      variant: {
        default: 'border-[var(--color-input-border)] bg-[var(--color-input-bg)] text-[var(--color-text-primary)] focus-visible:ring-[var(--color-primary)]',
        error: 'border-[var(--color-danger)] bg-[var(--color-input-bg)] text-[var(--color-text-primary)] focus-visible:ring-[var(--color-danger)]',
        success: 'border-[var(--color-success)] bg-[var(--color-input-bg)] text-[var(--color-text-primary)] focus-visible:ring-[var(--color-success)]'
      },
      size: {
        sm: 'h-8 px-2 text-xs',
        default: 'h-10 px-3 py-2',
        lg: 'h-12 px-4 text-base',
        compact: 'h-8 px-1 text-sm' // For trading inputs with minimal padding
      }
    },
    defaultVariants: {
      variant: 'default',
      size: 'default'
    }
  }
);

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement>,
    VariantProps<typeof inputVariants> {
  label?: string;
  error?: string;
  success?: string;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  suffix?: string;
  prefix?: string;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ 
    className, 
    variant, 
    size, 
    type = 'text', 
    label, 
    error, 
    success, 
    icon, 
    iconPosition = 'left',
    suffix,
    prefix,
    ...props 
  }, ref) => {
    // Determine variant based on error/success state
    const finalVariant = error ? 'error' : success ? 'success' : variant;

    return (
      <div className="w-full">
        {label && (
          <label className="text-sm font-medium text-[var(--color-text-primary)] mb-2 block">
            {label}
          </label>
        )}
        
        <div className="relative">
          {/* Prefix */}
          {prefix && (
            <div className="absolute left-3 top-1/2 -translate-y-1/2 text-[var(--color-text-muted)] text-sm">
              {prefix}
            </div>
          )}
          
          {/* Left Icon */}
          {icon && iconPosition === 'left' && (
            <div className="absolute left-3 top-1/2 -translate-y-1/2 text-[var(--color-text-muted)]">
              {icon}
            </div>
          )}
          
          <input
            type={type}
            className={clsx(
              inputVariants({ variant: finalVariant, size, className }),
              {
                'pl-10': (icon && iconPosition === 'left') || prefix,
                'pr-10': (icon && iconPosition === 'right') || suffix,
              }
            )}
            ref={ref}
            {...props}
          />
          
          {/* Right Icon */}
          {icon && iconPosition === 'right' && (
            <div className="absolute right-3 top-1/2 -translate-y-1/2 text-[var(--color-text-muted)]">
              {icon}
            </div>
          )}
          
          {/* Suffix */}
          {suffix && (
            <div className="absolute right-3 top-1/2 -translate-y-1/2 text-[var(--color-text-muted)] text-sm">
              {suffix}
            </div>
          )}
        </div>
        
        {/* Error Message */}
        {error && (
          <p className="text-[var(--color-danger)] text-xs mt-1 animate-fade-in">
            {error}
          </p>
        )}
        
        {/* Success Message */}
        {success && (
          <p className="text-[var(--color-success)] text-xs mt-1 animate-fade-in">
            {success}
          </p>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';

export { Input, inputVariants };

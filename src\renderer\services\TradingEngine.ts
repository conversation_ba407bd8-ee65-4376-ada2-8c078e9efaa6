// 🧠 Elite Trading Intelligence Engine
import { 
  calculateRSI, 
  calculateMACD, 
  calculateBollingerBands,
  calculateSMA,
  calculateEMA,
  calculateWMA,
  calculateStochastic,
  calculateWilliamsR,
  calculateATR,
  generateRSISignal,
  generateMACDSignal,
  generateBollingerSignal
} from '../../shared/utils/trading';
import { OHLC, TechnicalIndicator, SignalData, MarketData } from '../../shared/interfaces/trading';
import { TECHNICAL_INDICATORS, SIGNAL_STRENGTH } from '../../shared/constants/trading';

export interface TradingStrategy {
  name: string;
  enabled: boolean;
  weight: number; // 0-1, how much this strategy influences the final decision
  parameters: Record<string, number>;
}

export interface StrategyResult {
  strategy: string;
  signal: 'buy' | 'sell' | 'neutral';
  strength: number;
  confidence: number;
  indicators: Record<string, number>;
  timestamp: Date;
}

export interface CombinedSignal {
  action: 'buy' | 'sell' | 'neutral';
  confidence: number;
  strength: number;
  strategies: StrategyResult[];
  timestamp: Date;
}

export class TradingEngine {
  private strategies: Map<string, TradingStrategy> = new Map();
  private marketData: OHLC[] = [];
  private currentPrice: number = 0;
  private isActive: boolean = false;

  constructor() {
    this.initializeStrategies();
  }

  private initializeStrategies(): void {
    // RSI Strategy
    this.strategies.set('RSI', {
      name: 'RSI (Relative Strength Index)',
      enabled: true,
      weight: 0.15,
      parameters: {
        period: 14,
        oversoldLevel: 30,
        overboughtLevel: 70
      }
    });

    // MACD Strategy
    this.strategies.set('MACD', {
      name: 'MACD (Moving Average Convergence Divergence)',
      enabled: true,
      weight: 0.15,
      parameters: {
        fastPeriod: 12,
        slowPeriod: 26,
        signalPeriod: 9
      }
    });

    // Bollinger Bands Strategy
    this.strategies.set('BOLLINGER', {
      name: 'Bollinger Bands',
      enabled: true,
      weight: 0.15,
      parameters: {
        period: 20,
        stdDev: 2
      }
    });

    // Moving Averages Strategy
    this.strategies.set('MA_CROSS', {
      name: 'Moving Average Crossover',
      enabled: true,
      weight: 0.12,
      parameters: {
        fastPeriod: 10,
        slowPeriod: 20
      }
    });

    // Stochastic Oscillator Strategy
    this.strategies.set('STOCHASTIC', {
      name: 'Stochastic Oscillator',
      enabled: true,
      weight: 0.12,
      parameters: {
        period: 14,
        oversoldLevel: 20,
        overboughtLevel: 80
      }
    });

    // Williams %R Strategy
    this.strategies.set('WILLIAMS_R', {
      name: 'Williams %R',
      enabled: true,
      weight: 0.10,
      parameters: {
        period: 14,
        oversoldLevel: -80,
        overboughtLevel: -20
      }
    });

    // ATR-based Volatility Strategy
    this.strategies.set('ATR_VOLATILITY', {
      name: 'ATR Volatility',
      enabled: true,
      weight: 0.08,
      parameters: {
        period: 14,
        multiplier: 2
      }
    });

    // Opening Range Breakout (ORB) Strategy
    this.strategies.set('ORB', {
      name: 'Opening Range Breakout',
      enabled: true,
      weight: 0.13,
      parameters: {
        rangePeriod: 30, // minutes
        breakoutThreshold: 0.1 // percentage
      }
    });
  }

  // 📊 Update market data
  updateMarketData(newCandle: OHLC): void {
    this.marketData.push(newCandle);
    this.currentPrice = newCandle.close;
    
    // Keep only last 200 candles for performance
    if (this.marketData.length > 200) {
      this.marketData = this.marketData.slice(-200);
    }
  }

  updateCurrentPrice(price: number): void {
    this.currentPrice = price;
  }

  // 🎯 Generate trading signals
  generateSignals(): CombinedSignal {
    if (this.marketData.length < 50) {
      return {
        action: 'neutral',
        confidence: 0,
        strength: 0,
        strategies: [],
        timestamp: new Date()
      };
    }

    const strategyResults: StrategyResult[] = [];

    // Execute each enabled strategy
    for (const [key, strategy] of this.strategies) {
      if (!strategy.enabled) continue;

      let result: StrategyResult;

      switch (key) {
        case 'RSI':
          result = this.executeRSIStrategy(strategy);
          break;
        case 'MACD':
          result = this.executeMACDStrategy(strategy);
          break;
        case 'BOLLINGER':
          result = this.executeBollingerStrategy(strategy);
          break;
        case 'MA_CROSS':
          result = this.executeMAStrategy(strategy);
          break;
        case 'STOCHASTIC':
          result = this.executeStochasticStrategy(strategy);
          break;
        case 'WILLIAMS_R':
          result = this.executeWilliamsRStrategy(strategy);
          break;
        case 'ATR_VOLATILITY':
          result = this.executeATRStrategy(strategy);
          break;
        case 'ORB':
          result = this.executeORBStrategy(strategy);
          break;
        default:
          continue;
      }

      strategyResults.push(result);
    }

    // Combine signals using weighted voting
    return this.combineSignals(strategyResults);
  }

  // 📈 Individual Strategy Implementations
  private executeRSIStrategy(strategy: TradingStrategy): StrategyResult {
    const prices = this.marketData.map(candle => candle.close);
    const rsi = calculateRSI(prices, strategy.parameters.period);
    const signal = generateRSISignal(rsi);

    return {
      strategy: strategy.name,
      signal: signal.signal,
      strength: signal.strength,
      confidence: this.calculateConfidence(signal.strength),
      indicators: { rsi },
      timestamp: new Date()
    };
  }

  private executeMACDStrategy(strategy: TradingStrategy): StrategyResult {
    const prices = this.marketData.map(candle => candle.close);
    const macd = calculateMACD(
      prices, 
      strategy.parameters.fastPeriod,
      strategy.parameters.slowPeriod,
      strategy.parameters.signalPeriod
    );
    
    const signal = generateMACDSignal(macd.macd, macd.signal, macd.histogram);

    return {
      strategy: strategy.name,
      signal: signal.signal,
      strength: signal.strength,
      confidence: this.calculateConfidence(signal.strength),
      indicators: { 
        macd: macd.macd, 
        signal: macd.signal, 
        histogram: macd.histogram 
      },
      timestamp: new Date()
    };
  }

  private executeBollingerStrategy(strategy: TradingStrategy): StrategyResult {
    const prices = this.marketData.map(candle => candle.close);
    const bands = calculateBollingerBands(
      prices, 
      strategy.parameters.period,
      strategy.parameters.stdDev
    );
    
    const signal = generateBollingerSignal(this.currentPrice, bands);

    return {
      strategy: strategy.name,
      signal: signal.signal,
      strength: signal.strength,
      confidence: this.calculateConfidence(signal.strength),
      indicators: { 
        upper: bands.upper, 
        middle: bands.middle, 
        lower: bands.lower,
        price: this.currentPrice
      },
      timestamp: new Date()
    };
  }

  private executeMAStrategy(strategy: TradingStrategy): StrategyResult {
    const prices = this.marketData.map(candle => candle.close);
    const fastMA = calculateEMA(prices, strategy.parameters.fastPeriod);
    const slowMA = calculateEMA(prices, strategy.parameters.slowPeriod);
    
    let signal: 'buy' | 'sell' | 'neutral' = 'neutral';
    let strength = SIGNAL_STRENGTH.NEUTRAL;

    if (fastMA > slowMA) {
      signal = 'buy';
      strength = Math.min(((fastMA - slowMA) / slowMA) * 1000, SIGNAL_STRENGTH.VERY_STRONG);
    } else if (fastMA < slowMA) {
      signal = 'sell';
      strength = Math.min(((slowMA - fastMA) / fastMA) * 1000, SIGNAL_STRENGTH.VERY_STRONG);
    }

    return {
      strategy: strategy.name,
      signal,
      strength,
      confidence: this.calculateConfidence(strength),
      indicators: { fastMA, slowMA },
      timestamp: new Date()
    };
  }

  private executeStochasticStrategy(strategy: TradingStrategy): StrategyResult {
    const highs = this.marketData.map(candle => candle.high);
    const lows = this.marketData.map(candle => candle.low);
    const closes = this.marketData.map(candle => candle.close);
    
    const stochastic = calculateStochastic(highs, lows, closes, strategy.parameters.period);
    
    let signal: 'buy' | 'sell' | 'neutral' = 'neutral';
    let strength = SIGNAL_STRENGTH.NEUTRAL;

    if (stochastic <= strategy.parameters.oversoldLevel) {
      signal = 'buy';
      strength = SIGNAL_STRENGTH.STRONG;
    } else if (stochastic >= strategy.parameters.overboughtLevel) {
      signal = 'sell';
      strength = SIGNAL_STRENGTH.STRONG;
    }

    return {
      strategy: strategy.name,
      signal,
      strength,
      confidence: this.calculateConfidence(strength),
      indicators: { stochastic },
      timestamp: new Date()
    };
  }

  private executeWilliamsRStrategy(strategy: TradingStrategy): StrategyResult {
    const highs = this.marketData.map(candle => candle.high);
    const lows = this.marketData.map(candle => candle.low);
    const closes = this.marketData.map(candle => candle.close);
    
    const williamsR = calculateWilliamsR(highs, lows, closes, strategy.parameters.period);
    
    let signal: 'buy' | 'sell' | 'neutral' = 'neutral';
    let strength = SIGNAL_STRENGTH.NEUTRAL;

    if (williamsR <= strategy.parameters.oversoldLevel) {
      signal = 'buy';
      strength = SIGNAL_STRENGTH.STRONG;
    } else if (williamsR >= strategy.parameters.overboughtLevel) {
      signal = 'sell';
      strength = SIGNAL_STRENGTH.STRONG;
    }

    return {
      strategy: strategy.name,
      signal,
      strength,
      confidence: this.calculateConfidence(strength),
      indicators: { williamsR },
      timestamp: new Date()
    };
  }

  private executeATRStrategy(strategy: TradingStrategy): StrategyResult {
    const atr = calculateATR(this.marketData, strategy.parameters.period);
    const recentCandles = this.marketData.slice(-5);
    const avgVolatility = recentCandles.reduce((sum, candle) => 
      sum + (candle.high - candle.low), 0) / recentCandles.length;
    
    let signal: 'buy' | 'sell' | 'neutral' = 'neutral';
    let strength = SIGNAL_STRENGTH.NEUTRAL;

    // High volatility suggests potential breakout
    if (avgVolatility > atr * strategy.parameters.multiplier) {
      // Determine direction based on recent price action
      const priceChange = this.currentPrice - this.marketData[this.marketData.length - 2].close;
      signal = priceChange > 0 ? 'buy' : 'sell';
      strength = SIGNAL_STRENGTH.WEAK;
    }

    return {
      strategy: strategy.name,
      signal,
      strength,
      confidence: this.calculateConfidence(strength),
      indicators: { atr, volatility: avgVolatility },
      timestamp: new Date()
    };
  }

  private executeORBStrategy(strategy: TradingStrategy): StrategyResult {
    // Simplified ORB - check if price breaks out of recent range
    const recentCandles = this.marketData.slice(-strategy.parameters.rangePeriod);
    const rangeHigh = Math.max(...recentCandles.map(c => c.high));
    const rangeLow = Math.min(...recentCandles.map(c => c.low));
    const rangeSize = rangeHigh - rangeLow;
    
    let signal: 'buy' | 'sell' | 'neutral' = 'neutral';
    let strength = SIGNAL_STRENGTH.NEUTRAL;

    const breakoutThreshold = rangeSize * (strategy.parameters.breakoutThreshold / 100);

    if (this.currentPrice > rangeHigh + breakoutThreshold) {
      signal = 'buy';
      strength = SIGNAL_STRENGTH.STRONG;
    } else if (this.currentPrice < rangeLow - breakoutThreshold) {
      signal = 'sell';
      strength = SIGNAL_STRENGTH.STRONG;
    }

    return {
      strategy: strategy.name,
      signal,
      strength,
      confidence: this.calculateConfidence(strength),
      indicators: { rangeHigh, rangeLow, currentPrice: this.currentPrice },
      timestamp: new Date()
    };
  }

  // 🎯 Combine multiple strategy signals
  private combineSignals(results: StrategyResult[]): CombinedSignal {
    let buyScore = 0;
    let sellScore = 0;
    let totalWeight = 0;

    for (const result of results) {
      const strategy = this.strategies.get(result.strategy.split(' ')[0]);
      if (!strategy) continue;

      const weight = strategy.weight;
      const confidence = result.confidence / 100;
      const weightedScore = weight * confidence;

      if (result.signal === 'buy') {
        buyScore += weightedScore;
      } else if (result.signal === 'sell') {
        sellScore += weightedScore;
      }

      totalWeight += weight;
    }

    // Normalize scores
    buyScore = buyScore / totalWeight;
    sellScore = sellScore / totalWeight;

    // Determine final signal
    let action: 'buy' | 'sell' | 'neutral' = 'neutral';
    let confidence = 0;
    let strength = 0;

    const threshold = 0.3; // Minimum confidence threshold

    if (buyScore > sellScore && buyScore > threshold) {
      action = 'buy';
      confidence = buyScore * 100;
      strength = Math.min(buyScore * 100, 100);
    } else if (sellScore > buyScore && sellScore > threshold) {
      action = 'sell';
      confidence = sellScore * 100;
      strength = Math.min(sellScore * 100, 100);
    }

    return {
      action,
      confidence,
      strength,
      strategies: results,
      timestamp: new Date()
    };
  }

  private calculateConfidence(strength: number): number {
    // Convert strength (0-100) to confidence percentage
    return Math.min(Math.max(strength, 0), 100);
  }

  // 🎛️ Strategy Management
  enableStrategy(strategyName: string): void {
    const strategy = this.strategies.get(strategyName);
    if (strategy) {
      strategy.enabled = true;
    }
  }

  disableStrategy(strategyName: string): void {
    const strategy = this.strategies.get(strategyName);
    if (strategy) {
      strategy.enabled = false;
    }
  }

  updateStrategyWeight(strategyName: string, weight: number): void {
    const strategy = this.strategies.get(strategyName);
    if (strategy) {
      strategy.weight = Math.max(0, Math.min(1, weight));
    }
  }

  updateStrategyParameters(strategyName: string, parameters: Record<string, number>): void {
    const strategy = this.strategies.get(strategyName);
    if (strategy) {
      strategy.parameters = { ...strategy.parameters, ...parameters };
    }
  }

  // 🔧 Utility Methods
  getStrategies(): TradingStrategy[] {
    return Array.from(this.strategies.values());
  }

  getStrategyStatus(strategyName: string): TradingStrategy | null {
    return this.strategies.get(strategyName) || null;
  }

  setActive(active: boolean): void {
    this.isActive = active;
  }

  isEngineActive(): boolean {
    return this.isActive;
  }

  getMarketDataLength(): number {
    return this.marketData.length;
  }

  getCurrentPrice(): number {
    return this.currentPrice;
  }
}

export default TradingEngine;

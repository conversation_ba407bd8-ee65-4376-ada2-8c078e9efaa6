// 🎯 Elite Card Component
import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { clsx } from 'clsx';

const cardVariants = cva(
  'rounded-lg border transition-all duration-200',
  {
    variants: {
      variant: {
        default: 'bg-[var(--color-card-bg)] border-[var(--color-card-border)]',
        surface: 'bg-[var(--color-surface)] border-[var(--color-border)]',
        glass: 'bg-[rgba(22,27,34,0.8)] border-[var(--color-border)] backdrop-blur-sm',
        trading: 'bg-[var(--color-card-bg)] border-[var(--color-card-border)] hover:border-[var(--color-primary)] hover:shadow-lg hover:shadow-[rgba(46,160,67,0.1)]',
        success: 'bg-[var(--color-card-bg)] border-[var(--color-success)] shadow-sm shadow-[rgba(46,160,67,0.1)]',
        danger: 'bg-[var(--color-card-bg)] border-[var(--color-danger)] shadow-sm shadow-[rgba(248,81,73,0.1)]',
        warning: 'bg-[var(--color-card-bg)] border-[var(--color-warning)] shadow-sm shadow-[rgba(210,153,34,0.1)]'
      },
      size: {
        sm: 'p-3',
        default: 'p-4',
        lg: 'p-6',
        xl: 'p-8',
        compact: 'p-2' // For tight spaces in trading interface
      },
      hover: {
        none: '',
        lift: 'hover:-translate-y-1 hover:shadow-lg',
        glow: 'hover:shadow-lg hover:shadow-[var(--color-primary)]/20',
        border: 'hover:border-[var(--color-primary)]'
      }
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
      hover: 'none'
    }
  }
);

export interface CardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cardVariants> {
  title?: string;
  subtitle?: string;
  icon?: React.ReactNode;
  actions?: React.ReactNode;
  footer?: React.ReactNode;
  loading?: boolean;
}

const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({ 
    className, 
    variant, 
    size, 
    hover,
    title, 
    subtitle, 
    icon, 
    actions, 
    footer, 
    loading,
    children, 
    ...props 
  }, ref) => {
    return (
      <div
        ref={ref}
        className={clsx(cardVariants({ variant, size, hover, className }))}
        {...props}
      >
        {/* Loading Overlay */}
        {loading && (
          <div className="absolute inset-0 bg-[var(--color-surface)]/80 backdrop-blur-sm rounded-lg flex items-center justify-center z-10">
            <div className="flex items-center gap-2 text-[var(--color-text-secondary)]">
              <svg
                className="h-5 w-5 animate-spin"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                />
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                />
              </svg>
              <span className="text-sm">Loading...</span>
            </div>
          </div>
        )}

        {/* Header */}
        {(title || subtitle || icon || actions) && (
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-start gap-3">
              {icon && (
                <div className="text-[var(--color-text-secondary)] mt-0.5">
                  {icon}
                </div>
              )}
              <div>
                {title && (
                  <h3 className="text-lg font-semibold text-[var(--color-text-primary)] leading-tight">
                    {title}
                  </h3>
                )}
                {subtitle && (
                  <p className="text-sm text-[var(--color-text-secondary)] mt-1">
                    {subtitle}
                  </p>
                )}
              </div>
            </div>
            {actions && (
              <div className="flex items-center gap-2">
                {actions}
              </div>
            )}
          </div>
        )}

        {/* Content */}
        <div className="relative">
          {children}
        </div>

        {/* Footer */}
        {footer && (
          <div className="mt-4 pt-4 border-t border-[var(--color-border)]">
            {footer}
          </div>
        )}
      </div>
    );
  }
);

Card.displayName = 'Card';

// Card Header Component
const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={clsx('flex flex-col space-y-1.5 pb-4', className)}
    {...props}
  />
));
CardHeader.displayName = 'CardHeader';

// Card Title Component
const CardTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={clsx('text-lg font-semibold text-[var(--color-text-primary)] leading-none tracking-tight', className)}
    {...props}
  />
));
CardTitle.displayName = 'CardTitle';

// Card Description Component
const CardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={clsx('text-sm text-[var(--color-text-secondary)]', className)}
    {...props}
  />
));
CardDescription.displayName = 'CardDescription';

// Card Content Component
const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={clsx('', className)} {...props} />
));
CardContent.displayName = 'CardContent';

// Card Footer Component
const CardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={clsx('flex items-center pt-4 mt-4 border-t border-[var(--color-border)]', className)}
    {...props}
  />
));
CardFooter.displayName = 'CardFooter';

export { 
  Card, 
  CardHeader, 
  CardTitle, 
  CardDescription, 
  CardContent, 
  CardFooter,
  cardVariants 
};

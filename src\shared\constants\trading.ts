// 🎯 Elite Trading Bot Constants

export const WEBSOCKET_CONFIG = {
  URL: 'wss://demo-api-eu.po.market',
  ORIGIN: 'https://pocketoption.com',
  HEARTBEAT_INTERVAL: 20000, // 20 seconds
  CONNECTION_TIMEOUT: 10000,
  RECONNECT_DELAY: 5000,
  MAX_RECONNECT_ATTEMPTS: 5
} as const;

export const TRADE_DURATIONS = {
  S5: 'S5',
  S15: 'S15', 
  S30: 'S30',
  M1: 'M1',
  M5: 'M5',
  M15: 'M15',
  M30: 'M30',
  H1: 'H1'
} as const;

export const TRADE_ACTIONS = {
  CALL: 'call',
  PUT: 'put',
  BUY: 'BUY',
  SELL: 'SELL'
} as const;

export const MARTINGALE_MODES = {
  NONE: 'none',
  MARTINGALE: 'martingale',
  ANTI_MARTINGALE: 'anti-martingale',
  FIBONACCI: 'fibonacci',
  DALEMBERT: 'da<PERSON>bert'
} as const;

export const ASSET_CATEGORIES = {
  CURRENCY: 'currency',
  CRYPTO: 'crypto',
  COMMODITIES: 'commodities',
  INDICES: 'indices',
  STOCKS: 'stocks'
} as const;

export const TRADE_STATUS = {
  PENDING: 'pending',
  ACTIVE: 'active',
  CLOSED: 'closed',
  CANCELLED: 'cancelled'
} as const;

export const LOG_LEVELS = {
  DEBUG: 'debug',
  INFO: 'info',
  WARN: 'warn',
  ERROR: 'error'
} as const;

export const LOG_CATEGORIES = {
  TRADING: 'trading',
  SYSTEM: 'system',
  WEBSOCKET: 'websocket',
  DATABASE: 'database',
  STRATEGY: 'strategy',
  RISK: 'risk'
} as const;

export const TECHNICAL_INDICATORS = {
  RSI: 'RSI',
  MACD: 'MACD',
  BOLLINGER_BANDS: 'BOLLINGER_BANDS',
  SMA: 'SMA',
  EMA: 'EMA',
  WMA: 'WMA',
  STOCHASTIC: 'STOCHASTIC',
  WILLIAMS_R: 'WILLIAMS_R',
  ATR: 'ATR',
  ORB: 'ORB'
} as const;

export const SIGNAL_STRENGTH = {
  VERY_WEAK: 0,
  WEAK: 25,
  NEUTRAL: 50,
  STRONG: 75,
  VERY_STRONG: 100
} as const;

export const RISK_LEVELS = {
  VERY_LOW: 'very_low',
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  VERY_HIGH: 'very_high'
} as const;

export const DEFAULT_TRADING_CONFIG = {
  tradeCapital: 1000,
  targetProfit: 100,
  tradeAmount: 10,
  tradeDuration: TRADE_DURATIONS.M1,
  stopLoss: 500,
  martingaleMode: MARTINGALE_MODES.NONE,
  maxTradesPerSession: 50,
  drawdownLimit: 20
} as const;

export const DEFAULT_RISK_CONFIG = {
  maxDrawdown: 20,
  dailyLossLimit: 500,
  emergencyStop: true,
  positionSizing: 'fixed',
  riskPerTrade: 2
} as const;

export const UI_THEMES = {
  DARK: 'dark',
  LIGHT: 'light'
} as const;

export const COLORS = {
  // Dark theme colors
  DARK: {
    PRIMARY: '#2ea043',
    DANGER: '#f85149',
    WARNING: '#d29922',
    SUCCESS: '#2ea043',
    INFO: '#58a6ff',
    BACKGROUND: '#0d1117',
    SURFACE: '#161b22',
    BORDER: '#30363d',
    TEXT_PRIMARY: '#f0f6fc',
    TEXT_SECONDARY: '#8b949e',
    TEXT_MUTED: '#6e7681'
  },
  // Light theme colors (for future use)
  LIGHT: {
    PRIMARY: '#2ea043',
    DANGER: '#d1242f',
    WARNING: '#bf8700',
    SUCCESS: '#2ea043',
    INFO: '#0969da',
    BACKGROUND: '#ffffff',
    SURFACE: '#f6f8fa',
    BORDER: '#d0d7de',
    TEXT_PRIMARY: '#24292f',
    TEXT_SECONDARY: '#656d76',
    TEXT_MUTED: '#8c959f'
  }
} as const;

export const PERFORMANCE_TARGETS = {
  UI_RESPONSE_TIME: 100, // ms
  WEBSOCKET_PROCESSING: 50, // ms
  MEMORY_FOOTPRINT: 500, // MB
  UPTIME_RELIABILITY: 95, // %
  MAX_TRADES_PER_SESSION: 1000
} as const;

export const VALIDATION_RULES = {
  MIN_TRADE_AMOUNT: 1,
  MAX_TRADE_AMOUNT: 10000,
  MIN_CAPITAL: 100,
  MAX_CAPITAL: 1000000,
  MIN_TARGET_PROFIT: 1,
  MAX_DRAWDOWN_LIMIT: 50,
  MIN_STOP_LOSS: 10,
  MAX_TRADES_PER_SESSION: 1000
} as const;

export const TIMEFRAMES = {
  S5: { label: '5 Seconds', value: 'S5', seconds: 5 },
  S15: { label: '15 Seconds', value: 'S15', seconds: 15 },
  S30: { label: '30 Seconds', value: 'S30', seconds: 30 },
  M1: { label: '1 Minute', value: 'M1', seconds: 60 },
  M5: { label: '5 Minutes', value: 'M5', seconds: 300 },
  M15: { label: '15 Minutes', value: 'M15', seconds: 900 },
  M30: { label: '30 Minutes', value: 'M30', seconds: 1800 },
  H1: { label: '1 Hour', value: 'H1', seconds: 3600 }
} as const;

export const EMOJI_INDICATORS = {
  SUCCESS: '✅',
  ERROR: '❌',
  WARNING: '⚠️',
  INFO: 'ℹ️',
  TRADE_UP: '📈',
  TRADE_DOWN: '📉',
  MONEY: '💰',
  ROCKET: '🚀',
  FIRE: '🔥',
  LIGHTNING: '⚡',
  SHIELD: '🛡️',
  BRAIN: '🧠',
  ROBOT: '🤖',
  CHART: '📊',
  BELL: '🔔',
  GEAR: '⚙️',
  LOCK: '🔒',
  KEY: '🔑',
  CONNECT: '🔌',
  DISCONNECT: '🔌',
  HEART: '💓'
} as const;

export const WEBSOCKET_EVENTS = {
  // Outgoing events
  AUTH: 'auth',
  OPEN_ORDER: 'openOrder',
  CLOSE_ORDER: 'closeOrder',
  GET_ASSETS: 'getAssets',
  LOAD_HISTORY: 'loadHistoryPeriodFast',
  SUBSCRIBE: 'subscribeMessage',
  HEARTBEAT: 'ps',

  // Incoming events
  SUCCESS_AUTH: 'successauth',
  SUCCESS_OPEN_ORDER: 'successopenOrder',
  SUCCESS_CLOSE_ORDER: 'successcloseOrder',
  SUCCESS_UPDATE_BALANCE: 'successupdateBalance',
  SUCCESS_UPDATE_PENDING: 'successupdatePending',
  UPDATE_STREAM: 'updateStream',
  UPDATE_ASSETS: 'updateAssets',
  UPDATE_CHARTS: 'updateCharts',
  UPDATE_OPENED_DEALS: 'updateOpenedDeals',
  UPDATE_CLOSED_DEALS: 'updateClosedDeals',
  LOAD_HISTORY_FAST: 'loadHistoryPeriodFast',
  UPDATE_HISTORY_NEW: 'updateHistoryNewFast'
} as const;

export const IPC_CHANNELS = {
  // WebSocket
  WS_CONNECT: 'ws:connect',
  WS_DISCONNECT: 'ws:disconnect',
  WS_STATUS: 'ws:status',
  WS_CONNECTED: 'ws:connected',
  WS_DISCONNECTED: 'ws:disconnected',
  WS_AUTHENTICATED: 'ws:authenticated',
  WS_ERROR: 'ws:error',

  // Trading Session
  SESSION_CREATE: 'session:create',
  SESSION_GET_ACTIVE: 'session:get-active',
  SESSION_UPDATE: 'session:update',
  SESSION_END: 'session:end',
  SESSION_CREATED: 'session:created',
  SESSION_UPDATED: 'session:updated',
  SESSION_ENDED: 'session:ended',

  // Trading
  TRADE_OPEN: 'trade:open',
  TRADE_CLOSE: 'trade:close',
  TRADE_GET_BY_SESSION: 'trade:get-by-session',
  TRADE_ORDER_OPENED: 'trade:order-opened',
  TRADE_ORDER_CLOSED: 'trade:order-closed',

  // Market Data
  MARKET_REQUEST_ASSETS: 'market:request-assets',
  MARKET_REQUEST_HISTORY: 'market:request-history',
  MARKET_SUBSCRIBE: 'market:subscribe',
  MARKET_PRICE_UPDATE: 'market:price-update',
  MARKET_ASSETS_UPDATED: 'market:assets-updated',
  MARKET_HISTORY_LOADED: 'market:history-loaded',
  MARKET_NEW_TICK: 'market:new-tick',

  // Account
  ACCOUNT_BALANCE_UPDATED: 'account:balance-updated',

  // Database
  DB_QUERY: 'db:query'
} as const;

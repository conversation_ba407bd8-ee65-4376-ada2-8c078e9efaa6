// 🧮 Elite Trading Utility Functions
import { OHLC, TechnicalIndicator, MarketData } from '../interfaces/trading';
import { TECHNICAL_INDICATORS, SIGNAL_STRENGTH } from '../constants/trading';

// 📊 Technical Indicator Calculations

/**
 * Calculate RSI (Relative Strength Index)
 * @param prices Array of closing prices
 * @param period RSI period (default: 14)
 * @returns RSI value (0-100)
 */
export function calculateRSI(prices: number[], period: number = 14): number {
  if (prices.length < period + 1) return 50;

  let gains = 0;
  let losses = 0;

  // Calculate initial average gain and loss
  for (let i = 1; i <= period; i++) {
    const change = prices[i] - prices[i - 1];
    if (change > 0) gains += change;
    else losses += Math.abs(change);
  }

  let avgGain = gains / period;
  let avgLoss = losses / period;

  // Calculate RSI for remaining periods
  for (let i = period + 1; i < prices.length; i++) {
    const change = prices[i] - prices[i - 1];
    const gain = change > 0 ? change : 0;
    const loss = change < 0 ? Math.abs(change) : 0;

    avgGain = (avgGain * (period - 1) + gain) / period;
    avgLoss = (avgLoss * (period - 1) + loss) / period;
  }

  if (avgLoss === 0) return 100;
  const rs = avgGain / avgLoss;
  return 100 - (100 / (1 + rs));
}

/**
 * Calculate MACD (Moving Average Convergence Divergence)
 * @param prices Array of closing prices
 * @param fastPeriod Fast EMA period (default: 12)
 * @param slowPeriod Slow EMA period (default: 26)
 * @param signalPeriod Signal line period (default: 9)
 * @returns MACD object with macd, signal, and histogram
 */
export function calculateMACD(
  prices: number[], 
  fastPeriod: number = 12, 
  slowPeriod: number = 26, 
  signalPeriod: number = 9
): { macd: number; signal: number; histogram: number } {
  const fastEMA = calculateEMA(prices, fastPeriod);
  const slowEMA = calculateEMA(prices, slowPeriod);
  const macd = fastEMA - slowEMA;

  // Calculate signal line (EMA of MACD)
  const macdValues = [];
  for (let i = slowPeriod - 1; i < prices.length; i++) {
    const fast = calculateEMA(prices.slice(0, i + 1), fastPeriod);
    const slow = calculateEMA(prices.slice(0, i + 1), slowPeriod);
    macdValues.push(fast - slow);
  }

  const signal = calculateEMA(macdValues, signalPeriod);
  const histogram = macd - signal;

  return { macd, signal, histogram };
}

/**
 * Calculate Bollinger Bands
 * @param prices Array of closing prices
 * @param period Period for moving average (default: 20)
 * @param stdDev Standard deviation multiplier (default: 2)
 * @returns Bollinger Bands object with upper, middle, and lower bands
 */
export function calculateBollingerBands(
  prices: number[], 
  period: number = 20, 
  stdDev: number = 2
): { upper: number; middle: number; lower: number } {
  const middle = calculateSMA(prices, period);
  const variance = calculateVariance(prices.slice(-period));
  const standardDeviation = Math.sqrt(variance);

  const upper = middle + (stdDev * standardDeviation);
  const lower = middle - (stdDev * standardDeviation);

  return { upper, middle, lower };
}

/**
 * Calculate Simple Moving Average (SMA)
 * @param prices Array of prices
 * @param period Period for average
 * @returns SMA value
 */
export function calculateSMA(prices: number[], period: number): number {
  if (prices.length < period) return prices[prices.length - 1] || 0;
  
  const slice = prices.slice(-period);
  return slice.reduce((sum, price) => sum + price, 0) / period;
}

/**
 * Calculate Exponential Moving Average (EMA)
 * @param prices Array of prices
 * @param period Period for average
 * @returns EMA value
 */
export function calculateEMA(prices: number[], period: number): number {
  if (prices.length === 0) return 0;
  if (prices.length === 1) return prices[0];

  const multiplier = 2 / (period + 1);
  let ema = prices[0];

  for (let i = 1; i < prices.length; i++) {
    ema = (prices[i] * multiplier) + (ema * (1 - multiplier));
  }

  return ema;
}

/**
 * Calculate Weighted Moving Average (WMA)
 * @param prices Array of prices
 * @param period Period for average
 * @returns WMA value
 */
export function calculateWMA(prices: number[], period: number): number {
  if (prices.length < period) return prices[prices.length - 1] || 0;

  const slice = prices.slice(-period);
  let weightedSum = 0;
  let weightSum = 0;

  for (let i = 0; i < slice.length; i++) {
    const weight = i + 1;
    weightedSum += slice[i] * weight;
    weightSum += weight;
  }

  return weightedSum / weightSum;
}

/**
 * Calculate Stochastic Oscillator
 * @param highs Array of high prices
 * @param lows Array of low prices
 * @param closes Array of closing prices
 * @param period Period for calculation (default: 14)
 * @returns Stochastic %K value
 */
export function calculateStochastic(
  highs: number[], 
  lows: number[], 
  closes: number[], 
  period: number = 14
): number {
  if (highs.length < period) return 50;

  const recentHighs = highs.slice(-period);
  const recentLows = lows.slice(-period);
  const currentClose = closes[closes.length - 1];

  const highestHigh = Math.max(...recentHighs);
  const lowestLow = Math.min(...recentLows);

  if (highestHigh === lowestLow) return 50;

  return ((currentClose - lowestLow) / (highestHigh - lowestLow)) * 100;
}

/**
 * Calculate Williams %R
 * @param highs Array of high prices
 * @param lows Array of low prices
 * @param closes Array of closing prices
 * @param period Period for calculation (default: 14)
 * @returns Williams %R value
 */
export function calculateWilliamsR(
  highs: number[], 
  lows: number[], 
  closes: number[], 
  period: number = 14
): number {
  const stochastic = calculateStochastic(highs, lows, closes, period);
  return stochastic - 100;
}

/**
 * Calculate Average True Range (ATR)
 * @param ohlcData Array of OHLC data
 * @param period Period for calculation (default: 14)
 * @returns ATR value
 */
export function calculateATR(ohlcData: OHLC[], period: number = 14): number {
  if (ohlcData.length < 2) return 0;

  const trueRanges = [];

  for (let i = 1; i < ohlcData.length; i++) {
    const current = ohlcData[i];
    const previous = ohlcData[i - 1];

    const tr1 = current.high - current.low;
    const tr2 = Math.abs(current.high - previous.close);
    const tr3 = Math.abs(current.low - previous.close);

    trueRanges.push(Math.max(tr1, tr2, tr3));
  }

  return calculateSMA(trueRanges, Math.min(period, trueRanges.length));
}

/**
 * Calculate variance for a set of numbers
 * @param numbers Array of numbers
 * @returns Variance
 */
export function calculateVariance(numbers: number[]): number {
  if (numbers.length === 0) return 0;

  const mean = numbers.reduce((sum, num) => sum + num, 0) / numbers.length;
  const squaredDifferences = numbers.map(num => Math.pow(num - mean, 2));
  
  return squaredDifferences.reduce((sum, diff) => sum + diff, 0) / numbers.length;
}

// 🎯 Signal Generation Functions

/**
 * Generate RSI signal
 * @param rsi RSI value
 * @returns Technical indicator signal
 */
export function generateRSISignal(rsi: number): TechnicalIndicator {
  let signal: 'buy' | 'sell' | 'neutral' = 'neutral';
  let strength = SIGNAL_STRENGTH.NEUTRAL;

  if (rsi <= 30) {
    signal = 'buy';
    strength = rsi <= 20 ? SIGNAL_STRENGTH.VERY_STRONG : SIGNAL_STRENGTH.STRONG;
  } else if (rsi >= 70) {
    signal = 'sell';
    strength = rsi >= 80 ? SIGNAL_STRENGTH.VERY_STRONG : SIGNAL_STRENGTH.STRONG;
  } else if (rsi <= 40) {
    signal = 'buy';
    strength = SIGNAL_STRENGTH.WEAK;
  } else if (rsi >= 60) {
    signal = 'sell';
    strength = SIGNAL_STRENGTH.WEAK;
  }

  return {
    name: TECHNICAL_INDICATORS.RSI,
    value: rsi,
    signal,
    strength
  };
}

/**
 * Generate MACD signal
 * @param macd MACD value
 * @param signal Signal line value
 * @param histogram Histogram value
 * @returns Technical indicator signal
 */
export function generateMACDSignal(
  macd: number, 
  signal: number, 
  histogram: number
): TechnicalIndicator {
  const crossover = macd > signal;
  const histogramPositive = histogram > 0;
  
  let signalType: 'buy' | 'sell' | 'neutral' = 'neutral';
  let strength = SIGNAL_STRENGTH.NEUTRAL;

  if (crossover && histogramPositive) {
    signalType = 'buy';
    strength = Math.abs(histogram) > 0.1 ? SIGNAL_STRENGTH.STRONG : SIGNAL_STRENGTH.WEAK;
  } else if (!crossover && !histogramPositive) {
    signalType = 'sell';
    strength = Math.abs(histogram) > 0.1 ? SIGNAL_STRENGTH.STRONG : SIGNAL_STRENGTH.WEAK;
  }

  return {
    name: TECHNICAL_INDICATORS.MACD,
    value: macd,
    signal: signalType,
    strength
  };
}

/**
 * Generate Bollinger Bands signal
 * @param price Current price
 * @param bands Bollinger Bands values
 * @returns Technical indicator signal
 */
export function generateBollingerSignal(
  price: number, 
  bands: { upper: number; middle: number; lower: number }
): TechnicalIndicator {
  let signal: 'buy' | 'sell' | 'neutral' = 'neutral';
  let strength = SIGNAL_STRENGTH.NEUTRAL;

  const upperDistance = (bands.upper - price) / (bands.upper - bands.middle);
  const lowerDistance = (price - bands.lower) / (bands.middle - bands.lower);

  if (price <= bands.lower) {
    signal = 'buy';
    strength = lowerDistance <= 0 ? SIGNAL_STRENGTH.VERY_STRONG : SIGNAL_STRENGTH.STRONG;
  } else if (price >= bands.upper) {
    signal = 'sell';
    strength = upperDistance <= 0 ? SIGNAL_STRENGTH.VERY_STRONG : SIGNAL_STRENGTH.STRONG;
  } else if (price < bands.middle) {
    signal = 'buy';
    strength = SIGNAL_STRENGTH.WEAK;
  } else if (price > bands.middle) {
    signal = 'sell';
    strength = SIGNAL_STRENGTH.WEAK;
  }

  return {
    name: TECHNICAL_INDICATORS.BOLLINGER_BANDS,
    value: price,
    signal,
    strength
  };
}

// 💰 Risk Management Functions

/**
 * Calculate position size based on risk percentage
 * @param accountBalance Current account balance
 * @param riskPercentage Risk percentage per trade
 * @param stopLossDistance Distance to stop loss in points
 * @returns Recommended position size
 */
export function calculatePositionSize(
  accountBalance: number,
  riskPercentage: number,
  stopLossDistance: number
): number {
  if (stopLossDistance <= 0) return 0;
  
  const riskAmount = accountBalance * (riskPercentage / 100);
  return Math.floor(riskAmount / stopLossDistance);
}

/**
 * Calculate drawdown percentage
 * @param peakBalance Peak account balance
 * @param currentBalance Current account balance
 * @returns Drawdown percentage
 */
export function calculateDrawdown(peakBalance: number, currentBalance: number): number {
  if (peakBalance <= 0) return 0;
  return ((peakBalance - currentBalance) / peakBalance) * 100;
}

/**
 * Calculate win rate
 * @param winningTrades Number of winning trades
 * @param totalTrades Total number of trades
 * @returns Win rate percentage
 */
export function calculateWinRate(winningTrades: number, totalTrades: number): number {
  if (totalTrades === 0) return 0;
  return (winningTrades / totalTrades) * 100;
}

/**
 * Calculate profit factor
 * @param grossProfit Total gross profit
 * @param grossLoss Total gross loss
 * @returns Profit factor
 */
export function calculateProfitFactor(grossProfit: number, grossLoss: number): number {
  if (grossLoss === 0) return grossProfit > 0 ? Infinity : 0;
  return grossProfit / Math.abs(grossLoss);
}

// 🔧 Utility Functions

/**
 * Format currency value
 * @param value Numeric value
 * @param currency Currency symbol (default: '$')
 * @param decimals Number of decimal places (default: 2)
 * @returns Formatted currency string
 */
export function formatCurrency(value: number, currency: string = '$', decimals: number = 2): string {
  return `${currency}${value.toFixed(decimals)}`;
}

/**
 * Format percentage value
 * @param value Numeric value
 * @param decimals Number of decimal places (default: 2)
 * @returns Formatted percentage string
 */
export function formatPercentage(value: number, decimals: number = 2): string {
  return `${value.toFixed(decimals)}%`;
}

/**
 * Generate unique trade ID
 * @returns Unique trade identifier
 */
export function generateTradeId(): string {
  return `trade_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Generate unique session ID
 * @returns Unique session identifier
 */
export function generateSessionId(): string {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Validate trading configuration
 * @param config Trading configuration object
 * @returns Validation result with errors if any
 */
export function validateTradingConfig(config: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!config.tradeCapital || config.tradeCapital < 100) {
    errors.push('Trade capital must be at least $100');
  }

  if (!config.tradeAmount || config.tradeAmount < 1) {
    errors.push('Trade amount must be at least $1');
  }

  if (config.tradeAmount > config.tradeCapital) {
    errors.push('Trade amount cannot exceed trade capital');
  }

  if (!config.targetProfit || config.targetProfit < 1) {
    errors.push('Target profit must be at least $1');
  }

  if (!config.stopLoss || config.stopLoss < 10) {
    errors.push('Stop loss must be at least $10');
  }

  if (config.drawdownLimit < 5 || config.drawdownLimit > 50) {
    errors.push('Drawdown limit must be between 5% and 50%');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

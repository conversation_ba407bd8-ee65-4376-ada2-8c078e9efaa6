// 🌐 Elite Connection Manager Component
import { useState, useEffect } from 'react';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { 
  Wifi, 
  WifiOff, 
  Key, 
  User, 
  Shield, 
  Activity,
  CheckCircle,
  XCircle,
  AlertCircle,
  Loader
} from 'lucide-react';
import webSocketService, { ConnectionConfig, ConnectionStatus } from '../../services/WebSocketService';

interface ConnectionManagerProps {
  onConnectionChange?: (connected: boolean, authenticated: boolean) => void;
  className?: string;
}

const ConnectionManager: React.FC<ConnectionManagerProps> = ({ 
  onConnectionChange,
  className = ''
}) => {
  const [config, setConfig] = useState<ConnectionConfig>({
    sessionToken: '',
    userId: '',
    isDemo: true
  });
  
  const [status, setStatus] = useState<ConnectionStatus>({
    connected: false,
    authenticated: false,
    lastPing: null,
    reconnectAttempts: 0
  });
  
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionLogs, setConnectionLogs] = useState<Array<{
    timestamp: Date;
    type: 'info' | 'success' | 'warning' | 'error';
    message: string;
  }>>([]);

  useEffect(() => {
    // Load saved configuration
    const savedConfig = localStorage.getItem('trading_bot_config');
    if (savedConfig) {
      try {
        const parsed = JSON.parse(savedConfig);
        setConfig(prev => ({ ...prev, ...parsed }));
      } catch (error) {
        console.error('Failed to load saved config:', error);
      }
    }

    // Set up event listeners
    const handleConnected = () => {
      setStatus(prev => ({ ...prev, connected: true, reconnectAttempts: 0 }));
      addLog('success', 'WebSocket connected successfully');
    };

    const handleDisconnected = (reason: string) => {
      setStatus(prev => ({ 
        ...prev, 
        connected: false, 
        authenticated: false,
        reconnectAttempts: prev.reconnectAttempts + 1
      }));
      setIsConnecting(false);
      addLog('warning', `WebSocket disconnected: ${reason}`);
    };

    const handleAuthenticated = (data: any) => {
      setStatus(prev => ({ ...prev, authenticated: true }));
      setIsConnecting(false);
      addLog('success', 'Authentication successful');
    };

    const handleError = (error: any) => {
      setIsConnecting(false);
      addLog('error', `Connection error: ${error.message || error}`);
    };

    const handleConnectionAttempt = (result: any) => {
      if (result.success) {
        addLog('info', 'Connection attempt successful, waiting for authentication...');
      } else {
        setIsConnecting(false);
        addLog('error', `Connection failed: ${result.error}`);
      }
    };

    // Register event listeners
    webSocketService.on('connected', handleConnected);
    webSocketService.on('disconnected', handleDisconnected);
    webSocketService.on('authenticated', handleAuthenticated);
    webSocketService.on('error', handleError);
    webSocketService.on('connection-attempt', handleConnectionAttempt);

    // Get initial status
    updateStatus();

    return () => {
      webSocketService.off('connected', handleConnected);
      webSocketService.off('disconnected', handleDisconnected);
      webSocketService.off('authenticated', handleAuthenticated);
      webSocketService.off('error', handleError);
      webSocketService.off('connection-attempt', handleConnectionAttempt);
    };
  }, []);

  useEffect(() => {
    onConnectionChange?.(status.connected, status.authenticated);
  }, [status.connected, status.authenticated, onConnectionChange]);

  const updateStatus = async () => {
    try {
      const currentStatus = await webSocketService.getStatus();
      setStatus(currentStatus);
    } catch (error) {
      console.error('Failed to get status:', error);
    }
  };

  const addLog = (type: 'info' | 'success' | 'warning' | 'error', message: string) => {
    setConnectionLogs(prev => [
      ...prev.slice(-9), // Keep only last 10 logs
      {
        timestamp: new Date(),
        type,
        message
      }
    ]);
  };

  const handleConnect = async () => {
    if (!config.sessionToken || !config.userId) {
      addLog('error', 'Session token and User ID are required');
      return;
    }

    setIsConnecting(true);
    addLog('info', 'Attempting to connect...');

    try {
      // Save configuration
      localStorage.setItem('trading_bot_config', JSON.stringify({
        sessionToken: config.sessionToken,
        userId: config.userId,
        isDemo: config.isDemo
      }));

      await webSocketService.connect(config);
    } catch (error) {
      setIsConnecting(false);
      addLog('error', `Connection failed: ${error.message}`);
    }
  };

  const handleDisconnect = async () => {
    try {
      await webSocketService.disconnect();
      addLog('info', 'Disconnected successfully');
    } catch (error) {
      addLog('error', `Disconnect failed: ${error.message}`);
    }
  };

  const handleStartMockData = () => {
    webSocketService.startMockData('EURUSD');
    addLog('info', 'Mock data stream started for development');
  };

  const handleStopMockData = () => {
    webSocketService.stopMockData();
    addLog('info', 'Mock data stream stopped');
  };

  const getStatusIcon = () => {
    if (isConnecting) {
      return <Loader className="h-5 w-5 animate-spin text-[var(--color-info)]" />;
    }
    
    if (status.authenticated) {
      return <CheckCircle className="h-5 w-5 text-[var(--color-success)]" />;
    }
    
    if (status.connected) {
      return <AlertCircle className="h-5 w-5 text-[var(--color-warning)]" />;
    }
    
    return <XCircle className="h-5 w-5 text-[var(--color-danger)]" />;
  };

  const getStatusText = () => {
    if (isConnecting) return 'Connecting...';
    if (status.authenticated) return 'Connected & Authenticated';
    if (status.connected) return 'Connected (Not Authenticated)';
    return 'Disconnected';
  };

  const getLogIcon = (type: string) => {
    switch (type) {
      case 'success': return <CheckCircle className="h-3 w-3 text-[var(--color-success)]" />;
      case 'warning': return <AlertCircle className="h-3 w-3 text-[var(--color-warning)]" />;
      case 'error': return <XCircle className="h-3 w-3 text-[var(--color-danger)]" />;
      default: return <Activity className="h-3 w-3 text-[var(--color-info)]" />;
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Connection Status */}
      <Card 
        variant={status.authenticated ? 'success' : status.connected ? 'warning' : 'danger'}
        title="🌐 Connection Status"
        icon={getStatusIcon()}
      >
        <div className="flex items-center justify-between mb-4">
          <div>
            <div className="font-semibold">{getStatusText()}</div>
            <div className="text-sm text-[var(--color-text-muted)]">
              {status.reconnectAttempts > 0 && `Reconnect attempts: ${status.reconnectAttempts}`}
            </div>
          </div>
          
          <div className="flex gap-2">
            {!status.connected ? (
              <Button
                variant="primary"
                onClick={handleConnect}
                loading={isConnecting}
                disabled={!config.sessionToken || !config.userId}
                icon={<Wifi className="h-4 w-4" />}
              >
                Connect
              </Button>
            ) : (
              <Button
                variant="danger"
                onClick={handleDisconnect}
                icon={<WifiOff className="h-4 w-4" />}
              >
                Disconnect
              </Button>
            )}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="flex gap-2">
          <Button
            variant="secondary"
            size="sm"
            onClick={updateStatus}
            icon={<Activity className="h-4 w-4" />}
          >
            Refresh
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={handleStartMockData}
            disabled={status.connected}
          >
            Start Mock Data
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={handleStopMockData}
          >
            Stop Mock Data
          </Button>
        </div>
      </Card>

      {/* Configuration */}
      <Card variant="trading" title="⚙️ Connection Configuration" icon={<Shield className="h-5 w-5" />}>
        <div className="space-y-3">
          <Input
            label="Session Token"
            type="password"
            value={config.sessionToken}
            onChange={(e) => setConfig(prev => ({ ...prev, sessionToken: e.target.value }))}
            placeholder="Enter your session token"
            icon={<Key className="h-4 w-4" />}
            disabled={status.connected}
          />
          
          <Input
            label="User ID"
            value={config.userId}
            onChange={(e) => setConfig(prev => ({ ...prev, userId: e.target.value }))}
            placeholder="Enter your user ID"
            icon={<User className="h-4 w-4" />}
            disabled={status.connected}
          />
          
          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              id="demo-mode"
              checked={config.isDemo}
              onChange={(e) => setConfig(prev => ({ ...prev, isDemo: e.target.checked }))}
              disabled={status.connected}
              className="rounded border-[var(--color-border)]"
            />
            <label htmlFor="demo-mode" className="text-sm">
              Demo Mode (Recommended for testing)
            </label>
          </div>
        </div>
      </Card>

      {/* Connection Logs */}
      <Card variant="glass" title="📋 Connection Logs" icon={<Activity className="h-5 w-5" />}>
        <div className="space-y-1 max-h-48 overflow-y-auto">
          {connectionLogs.length === 0 ? (
            <div className="text-center text-[var(--color-text-muted)] py-4">
              No connection logs yet
            </div>
          ) : (
            connectionLogs.map((log, index) => (
              <div 
                key={index}
                className="flex items-start gap-2 p-2 rounded text-sm hover:bg-[var(--color-surface)]"
              >
                {getLogIcon(log.type)}
                <div className="flex-1">
                  <div className="text-[var(--color-text-primary)]">{log.message}</div>
                  <div className="text-xs text-[var(--color-text-muted)]">
                    {log.timestamp.toLocaleTimeString()}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </Card>
    </div>
  );
};

export default ConnectionManager;

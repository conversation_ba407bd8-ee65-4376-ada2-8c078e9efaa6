// 🎯 Elite Signal Display Component
import { useState, useEffect } from 'react';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';
import { 
  TrendingUp, 
  TrendingDown, 
  Activity, 
  Zap,
  Clock,
  Target,
  BarChart3,
  AlertTriangle
} from 'lucide-react';
import { CombinedSignal, StrategyResult } from '../../services/TradingEngine';

interface SignalDisplayProps {
  signal: CombinedSignal | null;
  onExecuteSignal?: (action: 'buy' | 'sell', confidence: number) => void;
  className?: string;
}

const SignalDisplay: React.FC<SignalDisplayProps> = ({ 
  signal, 
  onExecuteSignal,
  className = ''
}) => {
  const [timeAgo, setTimeAgo] = useState<string>('');
  const [isFlashing, setIsFlashing] = useState(false);

  useEffect(() => {
    if (signal) {
      // Flash effect for new signals
      setIsFlashing(true);
      const flashTimeout = setTimeout(() => setIsFlashing(false), 1000);

      // Update time ago
      const updateTimeAgo = () => {
        const now = new Date();
        const diff = now.getTime() - signal.timestamp.getTime();
        const seconds = Math.floor(diff / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);

        if (hours > 0) {
          setTimeAgo(`${hours}h ${minutes % 60}m ago`);
        } else if (minutes > 0) {
          setTimeAgo(`${minutes}m ${seconds % 60}s ago`);
        } else {
          setTimeAgo(`${seconds}s ago`);
        }
      };

      updateTimeAgo();
      const interval = setInterval(updateTimeAgo, 1000);

      return () => {
        clearTimeout(flashTimeout);
        clearInterval(interval);
      };
    }
  }, [signal]);

  const getSignalColor = (action: string) => {
    switch (action) {
      case 'buy': return 'var(--color-success)';
      case 'sell': return 'var(--color-danger)';
      default: return 'var(--color-text-muted)';
    }
  };

  const getConfidenceLevel = (confidence: number) => {
    if (confidence >= 90) return { level: 'Very High', color: 'var(--color-success)' };
    if (confidence >= 75) return { level: 'High', color: 'var(--color-success)' };
    if (confidence >= 60) return { level: 'Medium', color: 'var(--color-warning)' };
    if (confidence >= 40) return { level: 'Low', color: 'var(--color-danger)' };
    return { level: 'Very Low', color: 'var(--color-danger)' };
  };

  const getStrengthBar = (strength: number) => {
    const percentage = Math.min(Math.max(strength, 0), 100);
    const color = percentage >= 70 ? 'var(--color-success)' : 
                  percentage >= 40 ? 'var(--color-warning)' : 'var(--color-danger)';
    
    return (
      <div className="w-full bg-[var(--color-border)] rounded-full h-2">
        <div 
          className="h-2 rounded-full transition-all duration-300"
          style={{ 
            width: `${percentage}%`, 
            backgroundColor: color 
          }}
        />
      </div>
    );
  };

  const handleExecuteSignal = () => {
    if (signal && signal.action !== 'neutral') {
      onExecuteSignal?.(signal.action, signal.confidence);
    }
  };

  if (!signal) {
    return (
      <Card 
        variant="glass" 
        className={`${className} opacity-50`}
        title="🎯 Signal Monitor"
        icon={<Target className="h-5 w-5" />}
      >
        <div className="text-center py-8">
          <Activity className="h-12 w-12 text-[var(--color-text-muted)] mx-auto mb-4" />
          <p className="text-[var(--color-text-muted)]">Waiting for trading signals...</p>
          <p className="text-sm text-[var(--color-text-muted)] mt-2">
            Ensure strategies are enabled and market data is available
          </p>
        </div>
      </Card>
    );
  }

  const confidenceLevel = getConfidenceLevel(signal.confidence);
  const signalColor = getSignalColor(signal.action);

  return (
    <Card 
      variant={signal.action === 'buy' ? 'success' : signal.action === 'sell' ? 'danger' : 'default'}
      className={`${className} ${isFlashing ? 'animate-pulse-success' : ''}`}
      title="🎯 Live Trading Signal"
      icon={<Target className="h-5 w-5" />}
    >
      {/* Main Signal Display */}
      <div className="text-center mb-6">
        <div className="flex items-center justify-center gap-3 mb-4">
          {signal.action === 'buy' ? (
            <TrendingUp className="h-12 w-12" style={{ color: signalColor }} />
          ) : signal.action === 'sell' ? (
            <TrendingDown className="h-12 w-12" style={{ color: signalColor }} />
          ) : (
            <Activity className="h-12 w-12 text-[var(--color-text-muted)]" />
          )}
          
          <div>
            <div 
              className="text-4xl font-bold"
              style={{ color: signalColor }}
            >
              {signal.action.toUpperCase()}
            </div>
            <div className="text-sm text-[var(--color-text-muted)] flex items-center gap-1">
              <Clock className="h-3 w-3" />
              {timeAgo}
            </div>
          </div>
        </div>

        {/* Confidence and Strength */}
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <div className="text-sm text-[var(--color-text-muted)]">Confidence</div>
            <div 
              className="text-2xl font-bold"
              style={{ color: confidenceLevel.color }}
            >
              {signal.confidence.toFixed(1)}%
            </div>
            <div 
              className="text-xs"
              style={{ color: confidenceLevel.color }}
            >
              {confidenceLevel.level}
            </div>
          </div>
          
          <div>
            <div className="text-sm text-[var(--color-text-muted)]">Strength</div>
            <div className="text-2xl font-bold">{signal.strength.toFixed(0)}</div>
            <div className="mt-1">
              {getStrengthBar(signal.strength)}
            </div>
          </div>
        </div>

        {/* Execute Button */}
        {signal.action !== 'neutral' && (
          <Button
            variant={signal.action === 'buy' ? 'buy' : 'sell'}
            size="lg"
            onClick={handleExecuteSignal}
            icon={signal.action === 'buy' ? <TrendingUp className="h-5 w-5" /> : <TrendingDown className="h-5 w-5" />}
            className="w-full"
          >
            Execute {signal.action.toUpperCase()} Signal
          </Button>
        )}
      </div>

      {/* Strategy Breakdown */}
      <div className="border-t border-[var(--color-border)] pt-4">
        <div className="flex items-center gap-2 mb-3">
          <BarChart3 className="h-4 w-4 text-[var(--color-text-muted)]" />
          <span className="text-sm font-medium">Strategy Breakdown</span>
        </div>
        
        <div className="space-y-2">
          {signal.strategies
            .filter(s => s.signal !== 'neutral')
            .sort((a, b) => b.confidence - a.confidence)
            .slice(0, 5)
            .map((strategy, index) => (
              <div 
                key={index}
                className="flex items-center justify-between p-2 rounded bg-[var(--color-surface)] border border-[var(--color-border)]"
              >
                <div className="flex items-center gap-2">
                  {strategy.signal === 'buy' ? (
                    <TrendingUp className="h-3 w-3 text-[var(--color-success)]" />
                  ) : (
                    <TrendingDown className="h-3 w-3 text-[var(--color-danger)]" />
                  )}
                  <span className="text-sm font-medium">
                    {strategy.strategy.split(' ')[0]}
                  </span>
                </div>
                
                <div className="flex items-center gap-2">
                  <span className="text-xs text-[var(--color-text-muted)]">
                    {strategy.confidence.toFixed(1)}%
                  </span>
                  <div className="w-12">
                    {getStrengthBar(strategy.strength)}
                  </div>
                </div>
              </div>
            ))}
        </div>

        {/* Signal Quality Indicator */}
        <div className="mt-4 p-3 rounded bg-[var(--color-surface)] border border-[var(--color-border)]">
          <div className="flex items-center gap-2 mb-2">
            {signal.confidence >= 70 ? (
              <Zap className="h-4 w-4 text-[var(--color-success)]" />
            ) : signal.confidence >= 50 ? (
              <AlertTriangle className="h-4 w-4 text-[var(--color-warning)]" />
            ) : (
              <AlertTriangle className="h-4 w-4 text-[var(--color-danger)]" />
            )}
            <span className="text-sm font-medium">Signal Quality</span>
          </div>
          
          <div className="text-xs text-[var(--color-text-muted)]">
            {signal.confidence >= 80 && (
              "🟢 Excellent signal with high confidence. Strong agreement across strategies."
            )}
            {signal.confidence >= 60 && signal.confidence < 80 && (
              "🟡 Good signal with moderate confidence. Consider market conditions."
            )}
            {signal.confidence >= 40 && signal.confidence < 60 && (
              "🟠 Weak signal with low confidence. Use caution and smaller position size."
            )}
            {signal.confidence < 40 && (
              "🔴 Very weak signal. Consider waiting for better opportunities."
            )}
          </div>
        </div>
      </div>
    </Card>
  );
};

export default SignalDisplay;

// 📊 Elite Market Data Display Component
import { useState, useEffect } from 'react';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';
import { Select, SelectOption } from '../ui/Select';
import { 
  TrendingUp, 
  TrendingDown, 
  Activity, 
  BarChart3,
  Clock,
  DollarSign,
  Zap
} from 'lucide-react';
import webSocketService from '../../services/WebSocketService';
import { MarketData, OHLC } from '../../shared/interfaces/trading';

interface MarketDataDisplayProps {
  onAssetChange?: (asset: string) => void;
  onPriceUpdate?: (data: MarketData) => void;
  onOHLCUpdate?: (data: OHLC) => void;
  className?: string;
}

const MarketDataDisplay: React.FC<MarketDataDisplayProps> = ({
  onAssetChange,
  onPriceUpdate,
  onOHLCUpdate,
  className = ''
}) => {
  const [selectedAsset, setSelectedAsset] = useState('EURUSD');
  const [currentPrice, setCurrentPrice] = useState<MarketData | null>(null);
  const [priceHistory, setPriceHistory] = useState<MarketData[]>([]);
  const [ohlcData, setOhlcData] = useState<OHLC[]>([]);
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [priceChange, setPriceChange] = useState<{
    value: number;
    percentage: number;
    direction: 'up' | 'down' | 'neutral';
  }>({ value: 0, percentage: 0, direction: 'neutral' });

  // Available assets
  const availableAssets: SelectOption[] = [
    { value: 'EURUSD', label: 'EUR/USD', icon: <DollarSign className="h-4 w-4" /> },
    { value: 'GBPUSD', label: 'GBP/USD', icon: <DollarSign className="h-4 w-4" /> },
    { value: 'USDJPY', label: 'USD/JPY', icon: <DollarSign className="h-4 w-4" /> },
    { value: 'AUDUSD', label: 'AUD/USD', icon: <DollarSign className="h-4 w-4" /> },
    { value: 'USDCAD', label: 'USD/CAD', icon: <DollarSign className="h-4 w-4" /> },
    { value: 'BTCUSD', label: 'BTC/USD', icon: <Activity className="h-4 w-4" /> },
    { value: 'ETHUSD', label: 'ETH/USD', icon: <Activity className="h-4 w-4" /> },
    { value: 'XAUUSD', label: 'Gold/USD', icon: <Zap className="h-4 w-4" /> },
  ];

  useEffect(() => {
    // Set up event listeners
    const handlePriceUpdate = (data: MarketData) => {
      if (data.asset === selectedAsset) {
        // Calculate price change
        if (currentPrice) {
          const change = data.price - currentPrice.price;
          const percentage = (change / currentPrice.price) * 100;
          setPriceChange({
            value: change,
            percentage,
            direction: change > 0 ? 'up' : change < 0 ? 'down' : 'neutral'
          });
        }

        setCurrentPrice(data);
        setLastUpdate(new Date());
        
        // Add to price history (keep last 100 points)
        setPriceHistory(prev => [...prev.slice(-99), data]);
        
        onPriceUpdate?.(data);
      }
    };

    const handleNewTick = (data: OHLC) => {
      if (data.timestamp) {
        setOhlcData(prev => [...prev.slice(-99), data]);
        onOHLCUpdate?.(data);
      }
    };

    const handleAssetSubscribed = (asset: string) => {
      if (asset === selectedAsset) {
        setIsSubscribed(true);
      }
    };

    const handleError = (error: any) => {
      console.error('Market data error:', error);
    };

    // Register event listeners
    webSocketService.on('price-update', handlePriceUpdate);
    webSocketService.on('new-tick', handleNewTick);
    webSocketService.on('asset-subscribed', handleAssetSubscribed);
    webSocketService.on('error', handleError);

    return () => {
      webSocketService.off('price-update', handlePriceUpdate);
      webSocketService.off('new-tick', handleNewTick);
      webSocketService.off('asset-subscribed', handleAssetSubscribed);
      webSocketService.off('error', handleError);
    };
  }, [selectedAsset, currentPrice, onPriceUpdate, onOHLCUpdate]);

  const handleAssetChange = async (asset: string) => {
    setSelectedAsset(asset);
    setCurrentPrice(null);
    setPriceHistory([]);
    setOhlcData([]);
    setIsSubscribed(false);
    setPriceChange({ value: 0, percentage: 0, direction: 'neutral' });
    
    onAssetChange?.(asset);

    // Subscribe to new asset if connected
    if (webSocketService.isAuthenticated()) {
      try {
        await webSocketService.subscribeToAsset(asset);
      } catch (error) {
        console.error('Failed to subscribe to asset:', error);
      }
    }
  };

  const handleSubscribe = async () => {
    if (!webSocketService.isAuthenticated()) {
      return;
    }

    try {
      await webSocketService.subscribeToAsset(selectedAsset);
    } catch (error) {
      console.error('Failed to subscribe:', error);
    }
  };

  const formatPrice = (price: number) => {
    return price.toFixed(5);
  };

  const formatChange = (change: number, percentage: number) => {
    const sign = change >= 0 ? '+' : '';
    return `${sign}${change.toFixed(5)} (${sign}${percentage.toFixed(2)}%)`;
  };

  const getPriceChangeColor = () => {
    switch (priceChange.direction) {
      case 'up': return 'text-[var(--color-success)]';
      case 'down': return 'text-[var(--color-danger)]';
      default: return 'text-[var(--color-text-muted)]';
    }
  };

  const getPriceChangeIcon = () => {
    switch (priceChange.direction) {
      case 'up': return <TrendingUp className="h-4 w-4 text-[var(--color-success)]" />;
      case 'down': return <TrendingDown className="h-4 w-4 text-[var(--color-danger)]" />;
      default: return <Activity className="h-4 w-4 text-[var(--color-text-muted)]" />;
    }
  };

  const getSparklineData = () => {
    if (priceHistory.length < 2) return '';
    
    const prices = priceHistory.map(p => p.price);
    const min = Math.min(...prices);
    const max = Math.max(...prices);
    const range = max - min;
    
    if (range === 0) return '';
    
    const points = prices.map((price, index) => {
      const x = (index / (prices.length - 1)) * 100;
      const y = 100 - ((price - min) / range) * 100;
      return `${x},${y}`;
    }).join(' ');
    
    return points;
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Asset Selection */}
      <Card variant="trading" title="📊 Market Data" icon={<BarChart3 className="h-5 w-5" />}>
        <div className="flex items-center gap-4 mb-4">
          <div className="flex-1">
            <Select
              label="Trading Asset"
              options={availableAssets}
              value={selectedAsset}
              onChange={handleAssetChange}
              size="default"
            />
          </div>
          
          <Button
            variant={isSubscribed ? "success" : "primary"}
            onClick={handleSubscribe}
            disabled={!webSocketService.isAuthenticated() || isSubscribed}
            icon={<Activity className="h-4 w-4" />}
          >
            {isSubscribed ? 'Subscribed' : 'Subscribe'}
          </Button>
        </div>

        {/* Current Price Display */}
        {currentPrice ? (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-sm text-[var(--color-text-muted)]">Current Price</div>
              <div className="text-3xl font-bold text-[var(--color-text-primary)]">
                {formatPrice(currentPrice.price)}
              </div>
              <div className="text-xs text-[var(--color-text-muted)]">
                Spread: {currentPrice.spread?.toFixed(5) || 'N/A'}
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-sm text-[var(--color-text-muted)]">Price Change</div>
              <div className={`text-xl font-semibold flex items-center justify-center gap-1 ${getPriceChangeColor()}`}>
                {getPriceChangeIcon()}
                {formatChange(priceChange.value, priceChange.percentage)}
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-sm text-[var(--color-text-muted)]">Bid / Ask</div>
              <div className="text-lg font-semibold">
                <span className="text-[var(--color-danger)]">
                  {currentPrice.bid?.toFixed(5) || 'N/A'}
                </span>
                <span className="text-[var(--color-text-muted)] mx-2">/</span>
                <span className="text-[var(--color-success)]">
                  {currentPrice.ask?.toFixed(5) || 'N/A'}
                </span>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <Activity className="h-12 w-12 text-[var(--color-text-muted)] mx-auto mb-4" />
            <p className="text-[var(--color-text-muted)]">
              {webSocketService.isAuthenticated() 
                ? 'Waiting for market data...' 
                : 'Connect to receive market data'
              }
            </p>
          </div>
        )}

        {/* Mini Sparkline Chart */}
        {priceHistory.length > 1 && (
          <div className="mt-4 p-3 bg-[var(--color-surface)] rounded border border-[var(--color-border)]">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Price Trend</span>
              <span className="text-xs text-[var(--color-text-muted)]">
                {priceHistory.length} data points
              </span>
            </div>
            <svg width="100%" height="60" className="overflow-visible">
              <polyline
                fill="none"
                stroke={priceChange.direction === 'up' ? 'var(--color-success)' : 
                       priceChange.direction === 'down' ? 'var(--color-danger)' : 
                       'var(--color-text-muted)'}
                strokeWidth="2"
                points={getSparklineData()}
                vectorEffect="non-scaling-stroke"
              />
            </svg>
          </div>
        )}
      </Card>

      {/* Market Statistics */}
      <Card variant="glass" title="📈 Market Statistics" icon={<BarChart3 className="h-5 w-5" />}>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <div className="text-[var(--color-text-muted)]">Data Points</div>
            <div className="text-lg font-semibold">{priceHistory.length}</div>
          </div>
          
          <div>
            <div className="text-[var(--color-text-muted)]">OHLC Candles</div>
            <div className="text-lg font-semibold">{ohlcData.length}</div>
          </div>
          
          <div>
            <div className="text-[var(--color-text-muted)]">Last Update</div>
            <div className="text-lg font-semibold">
              {lastUpdate ? lastUpdate.toLocaleTimeString() : 'N/A'}
            </div>
          </div>
          
          <div>
            <div className="text-[var(--color-text-muted)]">Status</div>
            <div className={`text-lg font-semibold ${isSubscribed ? 'text-[var(--color-success)]' : 'text-[var(--color-danger)]'}`}>
              {isSubscribed ? 'Live' : 'Offline'}
            </div>
          </div>
        </div>

        {/* Recent OHLC Data */}
        {ohlcData.length > 0 && (
          <div className="mt-4">
            <div className="text-sm font-medium mb-2">Recent OHLC Data</div>
            <div className="space-y-1">
              {ohlcData.slice(-3).reverse().map((candle, index) => (
                <div 
                  key={index}
                  className="flex items-center justify-between p-2 rounded bg-[var(--color-surface)] text-xs"
                >
                  <span className="text-[var(--color-text-muted)]">
                    {candle.timestamp.toLocaleTimeString()}
                  </span>
                  <div className="flex gap-4">
                    <span>O: {candle.open.toFixed(5)}</span>
                    <span className="text-[var(--color-success)]">H: {candle.high.toFixed(5)}</span>
                    <span className="text-[var(--color-danger)]">L: {candle.low.toFixed(5)}</span>
                    <span>C: {candle.close.toFixed(5)}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </Card>
    </div>
  );
};

export default MarketDataDisplay;

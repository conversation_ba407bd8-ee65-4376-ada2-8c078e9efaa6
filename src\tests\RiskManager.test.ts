// 🧪 Elite Risk Manager Test Suite
import { describe, it, expect, beforeEach, vi } from 'vitest';
import RiskManager from '../renderer/services/RiskManager';

describe('RiskManager', () => {
  let riskManager: RiskManager;

  beforeEach(() => {
    riskManager = new RiskManager({
      maxDailyLoss: 500,
      maxDrawdown: 20,
      maxConsecutiveLosses: 5,
      maxTradesPerDay: 100,
      maxPositionSize: 100,
      stopLossPercentage: 2,
      takeProfitPercentage: 4,
      riskRewardRatio: 2,
      maxRiskPerTrade: 2,
      emergencyStopEnabled: true,
      coolingOffPeriod: 30
    });
  });

  describe('Initialization', () => {
    it('should initialize with default parameters', () => {
      const params = riskManager.getRiskParameters();
      expect(params.maxDailyLoss).toBe(500);
      expect(params.maxDrawdown).toBe(20);
      expect(params.maxConsecutiveLosses).toBe(5);
      expect(params.emergencyStopEnabled).toBe(true);
    });

    it('should start with trading allowed', () => {
      const status = riskManager.getCurrentStatus();
      expect(status.isTradeAllowed).toBe(true);
      expect(status.currentRiskLevel).toBe('low');
      expect(status.dailyLoss).toBe(0);
      expect(status.consecutiveLosses).toBe(0);
    });
  });

  describe('Trade Validation', () => {
    it('should allow valid trades', () => {
      const result = riskManager.validateTrade(50, 'EURUSD');
      expect(result.allowed).toBe(true);
      expect(result.reason).toBeUndefined();
    });

    it('should reject trades exceeding position size', () => {
      const result = riskManager.validateTrade(150, 'EURUSD');
      expect(result.allowed).toBe(false);
      expect(result.reason).toContain('maximum position size');
      expect(result.suggestedAmount).toBe(100);
    });

    it('should reject trades exceeding risk per trade', () => {
      // With 10000 balance and 2% max risk, max trade should be 200
      riskManager.updateBalance(10000);
      const result = riskManager.validateTrade(250, 'EURUSD');
      expect(result.allowed).toBe(false);
      expect(result.reason).toContain('maximum risk per trade');
      expect(result.suggestedAmount).toBeDefined();
    });

    it('should reject trades when emergency stop is active', () => {
      riskManager.activateEmergencyStop();
      const result = riskManager.validateTrade(50, 'EURUSD');
      expect(result.allowed).toBe(false);
      expect(result.reason).toContain('Emergency stop');
    });

    it('should reject trades during cooling off period', () => {
      // Simulate consecutive losses to trigger cooling off
      for (let i = 0; i < 5; i++) {
        riskManager.processTrade({
          amount: 50,
          result: 'loss',
          profit: -50,
          asset: 'EURUSD',
          timestamp: new Date()
        });
      }

      const result = riskManager.validateTrade(50, 'EURUSD');
      expect(result.allowed).toBe(false);
      expect(result.reason).toContain('Cooling off period');
    });
  });

  describe('Trade Processing', () => {
    it('should process winning trades correctly', () => {
      const trade = {
        amount: 50,
        result: 'win' as const,
        profit: 40,
        asset: 'EURUSD',
        timestamp: new Date()
      };

      riskManager.processTrade(trade);
      const status = riskManager.getCurrentStatus();

      expect(status.tradesCount).toBe(1);
      expect(status.consecutiveLosses).toBe(0);
      expect(status.dailyLoss).toBe(0);
      expect(riskManager.getBalance()).toBe(10040); // 10000 + 40
    });

    it('should process losing trades correctly', () => {
      const trade = {
        amount: 50,
        result: 'loss' as const,
        profit: -50,
        asset: 'EURUSD',
        timestamp: new Date()
      };

      riskManager.processTrade(trade);
      const status = riskManager.getCurrentStatus();

      expect(status.tradesCount).toBe(1);
      expect(status.consecutiveLosses).toBe(1);
      expect(status.dailyLoss).toBe(50);
      expect(riskManager.getBalance()).toBe(9950); // 10000 - 50
    });

    it('should track consecutive losses', () => {
      for (let i = 0; i < 3; i++) {
        riskManager.processTrade({
          amount: 50,
          result: 'loss',
          profit: -50,
          asset: 'EURUSD',
          timestamp: new Date()
        });
      }

      const status = riskManager.getCurrentStatus();
      expect(status.consecutiveLosses).toBe(3);
    });

    it('should reset consecutive losses on win', () => {
      // First, create consecutive losses
      for (let i = 0; i < 3; i++) {
        riskManager.processTrade({
          amount: 50,
          result: 'loss',
          profit: -50,
          asset: 'EURUSD',
          timestamp: new Date()
        });
      }

      // Then a win
      riskManager.processTrade({
        amount: 50,
        result: 'win',
        profit: 40,
        asset: 'EURUSD',
        timestamp: new Date()
      });

      const status = riskManager.getCurrentStatus();
      expect(status.consecutiveLosses).toBe(0);
    });
  });

  describe('Risk Level Management', () => {
    it('should escalate risk level with increasing losses', () => {
      // Start with low risk
      expect(riskManager.getCurrentStatus().currentRiskLevel).toBe('low');

      // Create moderate losses (40% of daily limit)
      riskManager.processTrade({
        amount: 200,
        result: 'loss',
        profit: -200,
        asset: 'EURUSD',
        timestamp: new Date()
      });

      let status = riskManager.getCurrentStatus();
      expect(status.currentRiskLevel).toBe('medium');

      // Create high losses (70% of daily limit)
      riskManager.processTrade({
        amount: 150,
        result: 'loss',
        profit: -150,
        asset: 'EURUSD',
        timestamp: new Date()
      });

      status = riskManager.getCurrentStatus();
      expect(status.currentRiskLevel).toBe('high');
    });

    it('should calculate drawdown correctly', () => {
      // Set peak balance
      riskManager.updateBalance(12000);
      
      // Create losses to trigger drawdown
      riskManager.updateBalance(9600); // 20% drawdown

      const status = riskManager.getCurrentStatus();
      expect(status.currentDrawdown).toBe(20);
    });
  });

  describe('Emergency Stop', () => {
    it('should activate emergency stop on max drawdown', () => {
      const emergencyStopSpy = vi.fn();
      riskManager.on('emergency-stop', emergencyStopSpy);

      // Set peak balance and then trigger max drawdown
      riskManager.updateBalance(12000);
      riskManager.updateBalance(9600); // 20% drawdown (at limit)

      // Process a trade to trigger the check
      riskManager.processTrade({
        amount: 50,
        result: 'loss',
        profit: -50,
        asset: 'EURUSD',
        timestamp: new Date()
      });

      expect(emergencyStopSpy).toHaveBeenCalled();
      expect(riskManager.getCurrentStatus().isTradeAllowed).toBe(false);
    });

    it('should activate emergency stop on daily loss limit', () => {
      const emergencyStopSpy = vi.fn();
      riskManager.on('emergency-stop', emergencyStopSpy);

      // Reach daily loss limit
      riskManager.processTrade({
        amount: 500,
        result: 'loss',
        profit: -500,
        asset: 'EURUSD',
        timestamp: new Date()
      });

      const status = riskManager.getCurrentStatus();
      expect(status.isTradeAllowed).toBe(false);
    });

    it('should allow manual emergency stop activation/deactivation', () => {
      riskManager.activateEmergencyStop();
      expect(riskManager.getCurrentStatus().isTradeAllowed).toBe(false);

      riskManager.deactivateEmergencyStop();
      expect(riskManager.getCurrentStatus().isTradeAllowed).toBe(true);
    });
  });

  describe('Alert System', () => {
    it('should generate alerts for approaching limits', () => {
      const alertSpy = vi.fn();
      riskManager.on('risk-alert', alertSpy);

      // Create losses approaching daily limit (80%)
      riskManager.processTrade({
        amount: 400,
        result: 'loss',
        profit: -400,
        asset: 'EURUSD',
        timestamp: new Date()
      });

      expect(alertSpy).toHaveBeenCalled();
      const alertCall = alertSpy.mock.calls[0][0];
      expect(alertCall.type).toBe('warning');
      expect(alertCall.category).toBe('loss_limit');
    });

    it('should generate alerts for consecutive losses', () => {
      const alertSpy = vi.fn();
      riskManager.on('risk-alert', alertSpy);

      // Create max consecutive losses
      for (let i = 0; i < 5; i++) {
        riskManager.processTrade({
          amount: 50,
          result: 'loss',
          profit: -50,
          asset: 'EURUSD',
          timestamp: new Date()
        });
      }

      expect(alertSpy).toHaveBeenCalled();
      const status = riskManager.getCurrentStatus();
      expect(status.coolingOffUntil).toBeDefined();
    });

    it('should allow alert acknowledgment', () => {
      const alertSpy = vi.fn();
      riskManager.on('risk-alert', alertSpy);

      // Generate an alert
      riskManager.processTrade({
        amount: 400,
        result: 'loss',
        profit: -400,
        asset: 'EURUSD',
        timestamp: new Date()
      });

      const alertCall = alertSpy.mock.calls[0][0];
      const alertId = alertCall.id;

      const acknowledged = riskManager.acknowledgeAlert(alertId);
      expect(acknowledged).toBe(true);
    });
  });

  describe('Risk Metrics', () => {
    it('should calculate risk score correctly', () => {
      // Create some losses
      riskManager.processTrade({
        amount: 200,
        result: 'loss',
        profit: -200,
        asset: 'EURUSD',
        timestamp: new Date()
      });

      const metrics = riskManager.getRiskMetrics();
      expect(metrics.riskScore).toBeGreaterThan(0);
      expect(metrics.riskLevel).toBeDefined();
      expect(metrics.recommendations).toBeInstanceOf(Array);
    });

    it('should provide relevant recommendations', () => {
      // Create high drawdown scenario
      riskManager.updateBalance(12000);
      riskManager.updateBalance(10200); // 15% drawdown

      const metrics = riskManager.getRiskMetrics();
      expect(metrics.recommendations.length).toBeGreaterThan(0);
      expect(metrics.recommendations.some(r => r.includes('drawdown'))).toBe(true);
    });
  });

  describe('Parameter Updates', () => {
    it('should update risk parameters', () => {
      const newParams = {
        maxDailyLoss: 1000,
        maxDrawdown: 25
      };

      riskManager.updateRiskParameters(newParams);
      const params = riskManager.getRiskParameters();

      expect(params.maxDailyLoss).toBe(1000);
      expect(params.maxDrawdown).toBe(25);
    });

    it('should emit parameter update events', () => {
      const updateSpy = vi.fn();
      riskManager.on('risk-parameters-updated', updateSpy);

      riskManager.updateRiskParameters({ maxDailyLoss: 1000 });
      expect(updateSpy).toHaveBeenCalled();
    });
  });

  describe('Session Management', () => {
    it('should reset session correctly', () => {
      // Create some activity
      riskManager.processTrade({
        amount: 100,
        result: 'loss',
        profit: -100,
        asset: 'EURUSD',
        timestamp: new Date()
      });

      riskManager.resetSession();
      const status = riskManager.getCurrentStatus();

      expect(status.tradesCount).toBe(0);
      expect(status.dailyLoss).toBe(0);
      expect(status.consecutiveLosses).toBe(0);
      expect(status.activeAlerts).toHaveLength(0);
    });
  });

  describe('Performance', () => {
    it('should process trades quickly', () => {
      const startTime = performance.now();
      
      for (let i = 0; i < 100; i++) {
        riskManager.processTrade({
          amount: 50,
          result: i % 2 === 0 ? 'win' : 'loss',
          profit: i % 2 === 0 ? 40 : -50,
          asset: 'EURUSD',
          timestamp: new Date()
        });
      }
      
      const endTime = performance.now();
      const executionTime = endTime - startTime;
      
      expect(executionTime).toBeLessThan(100); // Should process 100 trades in less than 100ms
    });

    it('should validate trades quickly', () => {
      const startTime = performance.now();
      
      for (let i = 0; i < 1000; i++) {
        riskManager.validateTrade(50, 'EURUSD');
      }
      
      const endTime = performance.now();
      const executionTime = endTime - startTime;
      
      expect(executionTime).toBeLessThan(50); // Should validate 1000 trades in less than 50ms
    });
  });

  describe('Edge Cases', () => {
    it('should handle zero amount trades', () => {
      const result = riskManager.validateTrade(0, 'EURUSD');
      expect(result.allowed).toBe(true);
    });

    it('should handle negative profit correctly', () => {
      riskManager.processTrade({
        amount: 50,
        result: 'loss',
        profit: -100, // Loss greater than trade amount
        asset: 'EURUSD',
        timestamp: new Date()
      });

      const status = riskManager.getCurrentStatus();
      expect(status.dailyLoss).toBe(100);
      expect(riskManager.getBalance()).toBe(9900);
    });

    it('should handle very large profits', () => {
      riskManager.processTrade({
        amount: 50,
        result: 'win',
        profit: 10000,
        asset: 'EURUSD',
        timestamp: new Date()
      });

      expect(riskManager.getBalance()).toBe(20000);
      expect(riskManager.getPeakBalance()).toBe(20000);
    });
  });
});

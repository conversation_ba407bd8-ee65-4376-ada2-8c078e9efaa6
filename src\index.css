@import 'tailwindcss';

/* 🌙 Elite Trading Bot Dark Theme */
:root {
	/* Dark theme colors */
	--color-primary: #2ea043;
	--color-danger: #f85149;
	--color-warning: #d29922;
	--color-success: #2ea043;
	--color-info: #58a6ff;
	--color-background: #0d1117;
	--color-surface: #161b22;
	--color-border: #30363d;
	--color-text-primary: #f0f6fc;
	--color-text-secondary: #8b949e;
	--color-text-muted: #6e7681;

	/* Trading specific colors */
	--color-buy: #2ea043;
	--color-sell: #f85149;
	--color-profit: #2ea043;
	--color-loss: #f85149;

	/* Component specific */
	--color-input-bg: #21262d;
	--color-input-border: #30363d;
	--color-button-hover: rgba(46, 160, 67, 0.1);
	--color-card-bg: #161b22;
	--color-card-border: #30363d;
}

* {
	box-sizing: border-box;
}

body {
	margin: 0;
	padding: 0;
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans',
		'Droid Sans', 'Helvetica Neue', sans-serif;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	background-color: var(--color-background);
	color: var(--color-text-primary);
	overflow: hidden; /* Prevent scrollbars in Electron */
}

#root {
	height: 100vh;
	width: 100vw;
	display: flex;
	flex-direction: column;
}

/* Custom scrollbar */
::-webkit-scrollbar {
	width: 8px;
	height: 8px;
}

::-webkit-scrollbar-track {
	background: var(--color-surface);
}

::-webkit-scrollbar-thumb {
	background: var(--color-border);
	border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
	background: var(--color-text-muted);
}

/* Selection */
::selection {
	background-color: var(--color-primary);
	color: var(--color-background);
}

/* Focus styles */
*:focus {
	outline: 2px solid var(--color-primary);
	outline-offset: 2px;
}

/* Animations */
@keyframes pulse-success {
	0%,
	100% {
		background-color: var(--color-success);
	}
	50% {
		background-color: rgba(46, 160, 67, 0.7);
	}
}

@keyframes pulse-danger {
	0%,
	100% {
		background-color: var(--color-danger);
	}
	50% {
		background-color: rgba(248, 81, 73, 0.7);
	}
}

@keyframes slide-in {
	from {
		transform: translateX(-100%);
		opacity: 0;
	}
	to {
		transform: translateX(0);
		opacity: 1;
	}
}

@keyframes fade-in {
	from {
		opacity: 0;
	}
	to {
		opacity: 1;
	}
}

.animate-pulse-success {
	animation: pulse-success 2s infinite;
}

.animate-pulse-danger {
	animation: pulse-danger 2s infinite;
}

.animate-slide-in {
	animation: slide-in 0.3s ease-out;
}

.animate-fade-in {
	animation: fade-in 0.2s ease-out;
}

/* Utility classes */
.text-gradient {
	background: linear-gradient(135deg, var(--color-primary), var(--color-info));
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
}

.glass-effect {
	background: rgba(22, 27, 34, 0.8);
	backdrop-filter: blur(10px);
	border: 1px solid var(--color-border);
}

.trading-card {
	background: var(--color-card-bg);
	border: 1px solid var(--color-card-border);
	border-radius: 8px;
	padding: 16px;
	transition: all 0.2s ease;
}

.trading-card:hover {
	border-color: var(--color-primary);
	box-shadow: 0 4px 12px rgba(46, 160, 67, 0.1);
}

/* Button styles */
.btn-primary {
	background: var(--color-primary);
	color: white;
	border: none;
	padding: 8px 16px;
	border-radius: 6px;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.2s ease;
}

.btn-primary:hover {
	background: #26a641;
	transform: translateY(-1px);
}

.btn-danger {
	background: var(--color-danger);
	color: white;
	border: none;
	padding: 8px 16px;
	border-radius: 6px;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.2s ease;
}

.btn-danger:hover {
	background: #da3633;
	transform: translateY(-1px);
}

/* Input styles */
.input-primary {
	background: var(--color-input-bg);
	border: 1px solid var(--color-input-border);
	color: var(--color-text-primary);
	padding: 0 4px;
	border-radius: 4px;
	font-size: 14px;
	transition: border-color 0.2s ease;
}

.input-primary:focus {
	border-color: var(--color-primary);
	outline: none;
}

.input-primary::placeholder {
	color: var(--color-text-muted);
}

// 🗄️ Elite Trading Bot Database Models
import sqlite3 from 'sqlite3';
import { TradingSession, TradeResult, TradingConfig, AssetInfo } from '../../shared/interfaces/trading';

export class DatabaseManager {
  private db: sqlite3.Database;
  private dbPath: string;

  constructor(dbPath: string = './trading_bot.db') {
    this.dbPath = dbPath;
    this.db = new sqlite3.Database(dbPath);
    this.initializeTables();
  }

  private initializeTables(): void {
    // Trading sessions table
    this.db.run(`
      CREATE TABLE IF NOT EXISTS sessions (
        id TEXT PRIMARY KEY,
        start_time DATETIME NOT NULL,
        end_time DATETIME,
        initial_balance REAL NOT NULL,
        current_balance REAL NOT NULL,
        final_balance REAL,
        total_trades INTEGER DEFAULT 0,
        winning_trades INTEGER DEFAULT 0,
        losing_trades INTEGER DEFAULT 0,
        win_rate REAL DEFAULT 0,
        max_drawdown REAL DEFAULT 0,
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Individual trades table
    this.db.run(`
      CREATE TABLE IF NOT EXISTS trades (
        id TEXT PRIMARY KEY,
        session_id TEXT NOT NULL,
        asset TEXT NOT NULL,
        action TEXT NOT NULL,
        amount REAL NOT NULL,
        entry_time DATETIME NOT NULL,
        exit_time DATETIME,
        entry_price REAL NOT NULL,
        exit_price REAL,
        result REAL,
        strategy TEXT NOT NULL,
        is_demo BOOLEAN DEFAULT 1,
        status TEXT DEFAULT 'pending',
        confidence REAL,
        indicators TEXT, -- JSON string of indicator values
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY(session_id) REFERENCES sessions(id)
      )
    `);

    // Trading configurations table
    this.db.run(`
      CREATE TABLE IF NOT EXISTS trading_configs (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        trade_capital REAL NOT NULL,
        target_profit REAL NOT NULL,
        trade_amount REAL NOT NULL,
        trade_duration TEXT NOT NULL,
        stop_loss REAL NOT NULL,
        martingale_mode TEXT NOT NULL,
        max_trades_per_session INTEGER NOT NULL,
        drawdown_limit REAL NOT NULL,
        is_active BOOLEAN DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Assets table
    this.db.run(`
      CREATE TABLE IF NOT EXISTS assets (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        category TEXT NOT NULL,
        is_otc BOOLEAN DEFAULT 0,
        is_active BOOLEAN DEFAULT 1,
        spread REAL DEFAULT 0,
        volatility REAL DEFAULT 0,
        min_amount REAL DEFAULT 1,
        max_amount REAL DEFAULT 1000,
        last_updated DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Market data table for OHLC storage
    this.db.run(`
      CREATE TABLE IF NOT EXISTS market_data (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        asset TEXT NOT NULL,
        timestamp DATETIME NOT NULL,
        open REAL NOT NULL,
        high REAL NOT NULL,
        low REAL NOT NULL,
        close REAL NOT NULL,
        volume REAL,
        timeframe TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Performance metrics table
    this.db.run(`
      CREATE TABLE IF NOT EXISTS performance_metrics (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        session_id TEXT NOT NULL,
        timestamp DATETIME NOT NULL,
        balance REAL NOT NULL,
        profit_loss REAL NOT NULL,
        win_rate REAL NOT NULL,
        total_trades INTEGER NOT NULL,
        active_trades INTEGER NOT NULL,
        max_drawdown REAL NOT NULL,
        current_drawdown REAL NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY(session_id) REFERENCES sessions(id)
      )
    `);

    // System logs table
    this.db.run(`
      CREATE TABLE IF NOT EXISTS system_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        level TEXT NOT NULL,
        category TEXT NOT NULL,
        message TEXT NOT NULL,
        data TEXT, -- JSON string for additional data
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    console.log('✅ Database tables initialized successfully');
  }

  // Session management methods
  async createSession(session: Omit<TradingSession, 'id'>): Promise<string> {
    const id = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    return new Promise((resolve, reject) => {
      this.db.run(`
        INSERT INTO sessions (
          id, start_time, initial_balance, current_balance, 
          total_trades, winning_trades, losing_trades, win_rate, 
          max_drawdown, is_active
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        id, session.startTime.toISOString(), session.initialBalance, 
        session.currentBalance, session.totalTrades, session.winningTrades,
        session.losingTrades, session.winRate, session.maxDrawdown, 
        session.isActive ? 1 : 0
      ], function(err) {
        if (err) reject(err);
        else resolve(id);
      });
    });
  }

  async updateSession(sessionId: string, updates: Partial<TradingSession>): Promise<void> {
    const fields = [];
    const values = [];

    Object.entries(updates).forEach(([key, value]) => {
      if (key === 'id') return; // Skip ID updates
      
      const dbKey = key.replace(/([A-Z])/g, '_$1').toLowerCase();
      fields.push(`${dbKey} = ?`);
      
      if (value instanceof Date) {
        values.push(value.toISOString());
      } else if (typeof value === 'boolean') {
        values.push(value ? 1 : 0);
      } else {
        values.push(value);
      }
    });

    if (fields.length === 0) return;

    fields.push('updated_at = ?');
    values.push(new Date().toISOString());
    values.push(sessionId);

    return new Promise((resolve, reject) => {
      this.db.run(`
        UPDATE sessions SET ${fields.join(', ')} WHERE id = ?
      `, values, function(err) {
        if (err) reject(err);
        else resolve();
      });
    });
  }

  async getActiveSession(): Promise<TradingSession | null> {
    return new Promise((resolve, reject) => {
      this.db.get(`
        SELECT * FROM sessions WHERE is_active = 1 ORDER BY start_time DESC LIMIT 1
      `, (err, row: any) => {
        if (err) reject(err);
        else if (!row) resolve(null);
        else {
          resolve({
            id: row.id,
            startTime: new Date(row.start_time),
            endTime: row.end_time ? new Date(row.end_time) : undefined,
            initialBalance: row.initial_balance,
            currentBalance: row.current_balance,
            finalBalance: row.final_balance,
            totalTrades: row.total_trades,
            winningTrades: row.winning_trades,
            losingTrades: row.losing_trades,
            winRate: row.win_rate,
            maxDrawdown: row.max_drawdown,
            isActive: Boolean(row.is_active)
          });
        }
      });
    });
  }

  // Trade management methods
  async createTrade(trade: Omit<TradeResult, 'id'>): Promise<string> {
    const id = `trade_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    return new Promise((resolve, reject) => {
      this.db.run(`
        INSERT INTO trades (
          id, session_id, asset, action, amount, entry_time, 
          entry_price, strategy, is_demo, status, confidence, indicators
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        id, trade.sessionId, trade.asset, trade.action, trade.amount,
        trade.entryTime.toISOString(), trade.entryPrice, trade.strategy,
        trade.isDemo ? 1 : 0, trade.status, 0, '{}'
      ], function(err) {
        if (err) reject(err);
        else resolve(id);
      });
    });
  }

  async updateTrade(tradeId: string, updates: Partial<TradeResult>): Promise<void> {
    const fields = [];
    const values = [];

    Object.entries(updates).forEach(([key, value]) => {
      if (key === 'id') return;
      
      const dbKey = key.replace(/([A-Z])/g, '_$1').toLowerCase();
      fields.push(`${dbKey} = ?`);
      
      if (value instanceof Date) {
        values.push(value.toISOString());
      } else if (typeof value === 'boolean') {
        values.push(value ? 1 : 0);
      } else {
        values.push(value);
      }
    });

    if (fields.length === 0) return;

    fields.push('updated_at = ?');
    values.push(new Date().toISOString());
    values.push(tradeId);

    return new Promise((resolve, reject) => {
      this.db.run(`
        UPDATE trades SET ${fields.join(', ')} WHERE id = ?
      `, values, function(err) {
        if (err) reject(err);
        else resolve();
      });
    });
  }

  async getTradesBySession(sessionId: string): Promise<TradeResult[]> {
    return new Promise((resolve, reject) => {
      this.db.all(`
        SELECT * FROM trades WHERE session_id = ? ORDER BY entry_time DESC
      `, [sessionId], (err, rows: any[]) => {
        if (err) reject(err);
        else {
          const trades = rows.map(row => ({
            id: row.id,
            sessionId: row.session_id,
            asset: row.asset,
            action: row.action as any,
            amount: row.amount,
            entryTime: new Date(row.entry_time),
            exitTime: row.exit_time ? new Date(row.exit_time) : undefined,
            entryPrice: row.entry_price,
            exitPrice: row.exit_price,
            result: row.result,
            strategy: row.strategy,
            isDemo: Boolean(row.is_demo),
            status: row.status as any
          }));
          resolve(trades);
        }
      });
    });
  }

  // Cleanup and close
  close(): void {
    this.db.close((err) => {
      if (err) {
        console.error('❌ Error closing database:', err);
      } else {
        console.log('✅ Database connection closed');
      }
    });
  }
}

export default DatabaseManager;

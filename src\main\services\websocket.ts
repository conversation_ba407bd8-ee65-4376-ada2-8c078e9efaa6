// 🌐 Elite WebSocket Service for PocketOption API
import { io, Socket } from 'socket.io-client';
import { EventEmitter } from 'events';
import { TradeAction, TradeDuration, AssetInfo, MarketData, OHLC } from '../../shared/interfaces/trading';

export interface WebSocketConfig {
  url: string;
  origin: string;
  sessionToken?: string;
  userId?: string;
  isDemo: boolean;
}

export interface OpenOrderRequest {
  action: 'call' | 'put';
  amount: number;
  asset: string;
  isDemo: number;
  optionType: number;
  requestId: number;
  time: TradeDuration;
}

export interface AuthRequest {
  session: string;
  uid: string;
  isDemo: number;
  platform: number;
  isFastHistory: boolean;
}

export class PocketOptionWebSocket extends EventEmitter {
  private socket: Socket | null = null;
  private config: WebSocketConfig;
  private isConnected = false;
  private isAuthenticated = false;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 5000;

  constructor(config: WebSocketConfig) {
    super();
    this.config = config;
  }

  // 🔌 Connection Management
  async connect(): Promise<void> {
    try {
      console.log('🔌 Connecting to PocketOption WebSocket...');
      
      this.socket = io(this.config.url, {
        transports: ['websocket'],
        timeout: 10000,
        forceNew: true,
        extraHeaders: {
          'Origin': this.config.origin
        }
      });

      this.setupEventHandlers();
      
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Connection timeout'));
        }, 10000);

        this.socket!.on('connect', () => {
          clearTimeout(timeout);
          this.isConnected = true;
          this.reconnectAttempts = 0;
          console.log('✅ WebSocket connected successfully');
          this.emit('connected');
          resolve();
        });

        this.socket!.on('connect_error', (error) => {
          clearTimeout(timeout);
          console.error('❌ WebSocket connection error:', error);
          reject(error);
        });
      });
    } catch (error) {
      console.error('❌ Failed to connect to WebSocket:', error);
      throw error;
    }
  }

  // 🔐 Authentication
  async authenticate(): Promise<void> {
    if (!this.isConnected || !this.socket) {
      throw new Error('WebSocket not connected');
    }

    if (!this.config.sessionToken || !this.config.userId) {
      throw new Error('Session token and user ID required for authentication');
    }

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Authentication timeout'));
      }, 10000);

      // Listen for auth success
      this.socket!.once('successauth', (data) => {
        clearTimeout(timeout);
        this.isAuthenticated = true;
        console.log('✅ Authentication successful:', data);
        this.emit('authenticated', data);
        this.startHeartbeat();
        resolve();
      });

      // Send auth request
      const authData: AuthRequest = {
        session: this.config.sessionToken,
        uid: this.config.userId,
        isDemo: this.config.isDemo ? 1 : 0,
        platform: 2,
        isFastHistory: true
      };

      console.log('🔐 Sending authentication request...');
      this.socket!.emit('auth', authData);
    });
  }

  // 💓 Heartbeat Management
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      if (this.socket && this.isConnected) {
        this.socket.emit('ps');
      }
    }, 20000); // Every 20 seconds
  }

  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  // 📡 Event Handlers Setup
  private setupEventHandlers(): void {
    if (!this.socket) return;

    // Connection events
    this.socket.on('connect', () => {
      this.isConnected = true;
      this.emit('connected');
    });

    this.socket.on('disconnect', (reason) => {
      this.isConnected = false;
      this.isAuthenticated = false;
      this.stopHeartbeat();
      console.log('🔌 WebSocket disconnected:', reason);
      this.emit('disconnected', reason);
      this.handleReconnection();
    });

    // Success handlers
    this.socket.on('successauth', (data) => {
      this.emit('auth-success', data);
    });

    this.socket.on('successopenOrder', (data) => {
      console.log('✅ Order opened successfully:', data);
      this.emit('order-opened', data);
    });

    this.socket.on('successcloseOrder', (data) => {
      console.log('✅ Order closed successfully:', data);
      this.emit('order-closed', data);
    });

    this.socket.on('successupdateBalance', (data) => {
      this.emit('balance-updated', data);
    });

    this.socket.on('successupdatePending', (data) => {
      this.emit('pending-updated', data);
    });

    // Data stream handlers
    this.socket.on('updateStream', (data) => {
      this.emit('price-update', this.parseStreamData(data));
    });

    this.socket.on('updateAssets', (data) => {
      this.emit('assets-updated', this.parseAssetsData(data));
    });

    this.socket.on('updateCharts', (data) => {
      this.emit('charts-updated', data);
    });

    this.socket.on('updateOpenedDeals', (data) => {
      this.emit('opened-deals-updated', data);
    });

    this.socket.on('updateClosedDeals', (data) => {
      this.emit('closed-deals-updated', data);
    });

    this.socket.on('loadHistoryPeriodFast', (data) => {
      this.emit('history-loaded', this.parseHistoryData(data));
    });

    this.socket.on('updateHistoryNewFast', (data) => {
      this.emit('new-tick', this.parseTickData(data));
    });

    // Error handlers
    this.socket.on('error', (error) => {
      console.error('❌ WebSocket error:', error);
      this.emit('error', error);
    });
  }

  // 📊 Data Parsing Methods
  private parseStreamData(data: any): MarketData {
    return {
      asset: data.asset || data.a,
      timestamp: new Date(data.timestamp || data.t * 1000),
      price: data.price || data.p,
      bid: data.bid || data.b,
      ask: data.ask || data.a,
      spread: data.spread || data.s
    };
  }

  private parseAssetsData(data: any): AssetInfo[] {
    if (!Array.isArray(data)) return [];
    
    return data.map(asset => ({
      id: asset.id || asset.i,
      name: asset.name || asset.n,
      category: asset.category || asset.c,
      isOTC: Boolean(asset.isOTC || asset.otc),
      isActive: Boolean(asset.isActive || asset.active),
      spread: asset.spread || asset.s || 0,
      volatility: asset.volatility || asset.v || 0,
      minAmount: asset.minAmount || asset.min || 1,
      maxAmount: asset.maxAmount || asset.max || 1000
    }));
  }

  private parseHistoryData(data: any): OHLC[] {
    if (!data.history || !Array.isArray(data.history)) return [];
    
    return data.history.map((candle: any) => ({
      timestamp: new Date(candle.time * 1000),
      open: candle.open,
      high: candle.high,
      low: candle.low,
      close: candle.close,
      volume: candle.volume || 0
    }));
  }

  private parseTickData(data: any): MarketData {
    return {
      asset: data.asset,
      timestamp: new Date(data.time * 1000),
      price: data.price,
      volume: data.volume
    };
  }

  // 📈 Trading Operations
  async openOrder(orderData: OpenOrderRequest): Promise<void> {
    if (!this.isAuthenticated || !this.socket) {
      throw new Error('WebSocket not authenticated');
    }

    console.log('📈 Opening order:', orderData);
    this.socket.emit('openOrder', orderData);
  }

  async closeOrder(orderId: string): Promise<void> {
    if (!this.isAuthenticated || !this.socket) {
      throw new Error('WebSocket not authenticated');
    }

    console.log('📉 Closing order:', orderId);
    this.socket.emit('closeOrder', { id: orderId });
  }

  // 📊 Data Requests
  async requestAssets(): Promise<void> {
    if (!this.isAuthenticated || !this.socket) {
      throw new Error('WebSocket not authenticated');
    }

    this.socket.emit('getAssets');
  }

  async requestHistory(asset: string, timeframe: string, count: number = 100): Promise<void> {
    if (!this.isAuthenticated || !this.socket) {
      throw new Error('WebSocket not authenticated');
    }

    this.socket.emit('loadHistoryPeriodFast', {
      asset,
      period: timeframe,
      count,
      to: Math.floor(Date.now() / 1000)
    });
  }

  async subscribeToAsset(asset: string): Promise<void> {
    if (!this.isAuthenticated || !this.socket) {
      throw new Error('WebSocket not authenticated');
    }

    this.socket.emit('subscribeMessage', {
      name: 'updateStream',
      params: { asset }
    });
  }

  // 🔄 Reconnection Logic
  private async handleReconnection(): Promise<void> {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('❌ Max reconnection attempts reached');
      this.emit('max-reconnect-attempts');
      return;
    }

    this.reconnectAttempts++;
    console.log(`🔄 Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);

    setTimeout(async () => {
      try {
        await this.connect();
        if (this.config.sessionToken && this.config.userId) {
          await this.authenticate();
        }
      } catch (error) {
        console.error('❌ Reconnection failed:', error);
        this.handleReconnection();
      }
    }, this.reconnectDelay * this.reconnectAttempts);
  }

  // 🧹 Cleanup
  disconnect(): void {
    this.stopHeartbeat();
    
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    
    this.isConnected = false;
    this.isAuthenticated = false;
    this.reconnectAttempts = 0;
    
    console.log('🔌 WebSocket disconnected and cleaned up');
  }

  // Getters
  get connected(): boolean {
    return this.isConnected;
  }

  get authenticated(): boolean {
    return this.isAuthenticated;
  }
}

export default PocketOptionWebSocket;

// 🌐 Elite WebSocket Service for Renderer Process
import { EventEmitter } from 'events';
import { MarketData, OHLC, AssetInfo } from '../../shared/interfaces/trading';
import { IPC_CHANNELS } from '../../shared/constants/trading';

export interface ConnectionConfig {
  sessionToken: string;
  userId: string;
  isDemo: boolean;
}

export interface ConnectionStatus {
  connected: boolean;
  authenticated: boolean;
  lastPing: Date | null;
  reconnectAttempts: number;
}

export interface TradeRequest {
  asset: string;
  action: 'call' | 'put';
  amount: number;
  duration: string;
  strategy: string;
}

export class WebSocketService extends EventEmitter {
  private connectionStatus: ConnectionStatus = {
    connected: false,
    authenticated: false,
    lastPing: null,
    reconnectAttempts: 0
  };
  
  private subscribedAssets: Set<string> = new Set();
  private currentConfig: ConnectionConfig | null = null;

  constructor() {
    super();
    this.setupIpcListeners();
  }

  private setupIpcListeners(): void {
    // Connection events
    window.ipcRenderer.on(IPC_CHANNELS.WS_CONNECTED, () => {
      this.connectionStatus.connected = true;
      this.connectionStatus.reconnectAttempts = 0;
      this.emit('connected');
    });

    window.ipcRenderer.on(IPC_CHANNELS.WS_DISCONNECTED, (_, reason) => {
      this.connectionStatus.connected = false;
      this.connectionStatus.authenticated = false;
      this.emit('disconnected', reason);
    });

    window.ipcRenderer.on(IPC_CHANNELS.WS_AUTHENTICATED, (_, data) => {
      this.connectionStatus.authenticated = true;
      this.emit('authenticated', data);
    });

    window.ipcRenderer.on(IPC_CHANNELS.WS_ERROR, (_, error) => {
      this.emit('error', error);
    });

    // Trading events
    window.ipcRenderer.on(IPC_CHANNELS.TRADE_ORDER_OPENED, (_, data) => {
      this.emit('order-opened', data);
    });

    window.ipcRenderer.on(IPC_CHANNELS.TRADE_ORDER_CLOSED, (_, data) => {
      this.emit('order-closed', data);
    });

    // Market data events
    window.ipcRenderer.on(IPC_CHANNELS.MARKET_PRICE_UPDATE, (_, data) => {
      this.emit('price-update', this.parseMarketData(data));
    });

    window.ipcRenderer.on(IPC_CHANNELS.MARKET_ASSETS_UPDATED, (_, data) => {
      this.emit('assets-updated', data);
    });

    window.ipcRenderer.on(IPC_CHANNELS.MARKET_HISTORY_LOADED, (_, data) => {
      this.emit('history-loaded', data);
    });

    window.ipcRenderer.on(IPC_CHANNELS.MARKET_NEW_TICK, (_, data) => {
      this.emit('new-tick', this.parseTickData(data));
    });

    // Account events
    window.ipcRenderer.on(IPC_CHANNELS.ACCOUNT_BALANCE_UPDATED, (_, data) => {
      this.emit('balance-updated', data);
    });
  }

  // 🔌 Connection Management
  async connect(config: ConnectionConfig): Promise<boolean> {
    try {
      this.currentConfig = config;
      
      const result = await window.ipcRenderer.invoke(IPC_CHANNELS.WS_CONNECT, {
        url: 'wss://demo-api-eu.po.market',
        origin: 'https://pocketoption.com',
        sessionToken: config.sessionToken,
        userId: config.userId,
        isDemo: config.isDemo
      });

      if (result.success) {
        this.emit('connection-attempt', { success: true });
        return true;
      } else {
        this.emit('connection-attempt', { success: false, error: result.error });
        return false;
      }
    } catch (error) {
      this.emit('connection-attempt', { success: false, error: error.message });
      return false;
    }
  }

  async disconnect(): Promise<void> {
    try {
      await window.ipcRenderer.invoke(IPC_CHANNELS.WS_DISCONNECT);
      this.subscribedAssets.clear();
      this.currentConfig = null;
    } catch (error) {
      console.error('Failed to disconnect:', error);
    }
  }

  async getStatus(): Promise<ConnectionStatus> {
    try {
      const status = await window.ipcRenderer.invoke(IPC_CHANNELS.WS_STATUS);
      this.connectionStatus.connected = status.connected;
      this.connectionStatus.authenticated = status.authenticated;
      return this.connectionStatus;
    } catch (error) {
      return this.connectionStatus;
    }
  }

  // 📊 Market Data
  async requestAssets(): Promise<void> {
    if (!this.connectionStatus.authenticated) {
      throw new Error('Not authenticated');
    }

    try {
      await window.ipcRenderer.invoke(IPC_CHANNELS.MARKET_REQUEST_ASSETS);
    } catch (error) {
      this.emit('error', { type: 'request_assets_failed', error });
      throw error;
    }
  }

  async requestHistory(asset: string, timeframe: string, count: number = 100): Promise<void> {
    if (!this.connectionStatus.authenticated) {
      throw new Error('Not authenticated');
    }

    try {
      await window.ipcRenderer.invoke(IPC_CHANNELS.MARKET_REQUEST_HISTORY, asset, timeframe, count);
    } catch (error) {
      this.emit('error', { type: 'request_history_failed', error });
      throw error;
    }
  }

  async subscribeToAsset(asset: string): Promise<void> {
    if (!this.connectionStatus.authenticated) {
      throw new Error('Not authenticated');
    }

    try {
      await window.ipcRenderer.invoke(IPC_CHANNELS.MARKET_SUBSCRIBE, asset);
      this.subscribedAssets.add(asset);
      this.emit('asset-subscribed', asset);
    } catch (error) {
      this.emit('error', { type: 'subscribe_failed', error });
      throw error;
    }
  }

  // 📈 Trading Operations
  async openTrade(tradeRequest: TradeRequest): Promise<string> {
    if (!this.connectionStatus.authenticated) {
      throw new Error('Not authenticated');
    }

    try {
      const result = await window.ipcRenderer.invoke(IPC_CHANNELS.TRADE_OPEN, tradeRequest);
      
      if (result.success) {
        this.emit('trade-requested', { tradeId: result.tradeId, request: tradeRequest });
        return result.tradeId;
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      this.emit('error', { type: 'trade_open_failed', error });
      throw error;
    }
  }

  async closeTrade(tradeId: string): Promise<void> {
    if (!this.connectionStatus.authenticated) {
      throw new Error('Not authenticated');
    }

    try {
      const result = await window.ipcRenderer.invoke(IPC_CHANNELS.TRADE_CLOSE, tradeId);
      
      if (!result.success) {
        throw new Error(result.error);
      }
    } catch (error) {
      this.emit('error', { type: 'trade_close_failed', error });
      throw error;
    }
  }

  // 📊 Data Parsing
  private parseMarketData(data: any): MarketData {
    return {
      asset: data.asset || data.a,
      timestamp: new Date(data.timestamp || data.t * 1000),
      price: data.price || data.p,
      bid: data.bid || data.b,
      ask: data.ask || data.a,
      spread: data.spread || data.s
    };
  }

  private parseTickData(data: any): MarketData {
    return {
      asset: data.asset,
      timestamp: new Date(data.time * 1000),
      price: data.price,
      volume: data.volume
    };
  }

  // 🎛️ Utility Methods
  isConnected(): boolean {
    return this.connectionStatus.connected;
  }

  isAuthenticated(): boolean {
    return this.connectionStatus.authenticated;
  }

  getSubscribedAssets(): string[] {
    return Array.from(this.subscribedAssets);
  }

  getCurrentConfig(): ConnectionConfig | null {
    return this.currentConfig;
  }

  // 📊 Mock Data Generation (for development/testing)
  startMockData(asset: string = 'EURUSD'): void {
    if (this.mockDataInterval) {
      clearInterval(this.mockDataInterval);
    }

    let currentPrice = 1.0850;
    
    this.mockDataInterval = setInterval(() => {
      // Generate realistic price movement
      const change = (Math.random() - 0.5) * 0.0010; // ±0.0010 max change
      currentPrice += change;
      currentPrice = Math.max(1.0500, Math.min(1.1200, currentPrice)); // Keep in realistic range

      const mockData: MarketData = {
        asset,
        timestamp: new Date(),
        price: currentPrice,
        bid: currentPrice - 0.0002,
        ask: currentPrice + 0.0002,
        spread: 0.0004
      };

      this.emit('price-update', mockData);

      // Occasionally emit OHLC data (every 60 seconds)
      if (Math.random() < 0.1) {
        const ohlc: OHLC = {
          timestamp: new Date(),
          open: currentPrice - (Math.random() - 0.5) * 0.0020,
          high: currentPrice + Math.random() * 0.0015,
          low: currentPrice - Math.random() * 0.0015,
          close: currentPrice,
          volume: Math.floor(Math.random() * 1000) + 100
        };

        this.emit('new-tick', ohlc);
      }
    }, 1000); // Update every second
  }

  stopMockData(): void {
    if (this.mockDataInterval) {
      clearInterval(this.mockDataInterval);
      this.mockDataInterval = null;
    }
  }

  private mockDataInterval: NodeJS.Timeout | null = null;

  // 🧹 Cleanup
  cleanup(): void {
    this.stopMockData();
    this.removeAllListeners();
    this.subscribedAssets.clear();
    this.currentConfig = null;
  }
}

// Global WebSocket service instance
export const webSocketService = new WebSocketService();
export default webSocketService;

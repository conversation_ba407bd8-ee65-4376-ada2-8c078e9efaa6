// 🎛️ Elite Trading Dashboard Component
import { useState, useEffect } from 'react';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Select, SelectOption } from '../ui/Select';
import { 
  Play, 
  Square, 
  TrendingUp, 
  TrendingDown, 
  Settings, 
  Target,
  DollarSign,
  Clock,
  Activity,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { TradingConfig } from '../../shared/interfaces/trading';
import webSocketService, { TradeRequest } from '../../services/WebSocketService';
import SignalGenerator from '../../services/SignalGenerator';

interface TradingDashboardProps {
  signalGenerator: SignalGenerator;
  onTradeExecuted?: (trade: any) => void;
  className?: string;
}

const TradingDashboard: React.FC<TradingDashboardProps> = ({
  signalGenerator,
  onTradeExecuted,
  className = ''
}) => {
  const [isTrading, setIsTrading] = useState(false);
  const [tradingConfig, setTradingConfig] = useState<TradingConfig>({
    tradeCapital: 1000,
    targetProfit: 100,
    tradeAmount: 10,
    tradeDuration: 'M1',
    stopLoss: 500,
    martingaleMode: 'none',
    maxTradesPerSession: 50,
    drawdownLimit: 20
  });

  const [selectedAsset, setSelectedAsset] = useState('EURUSD');
  const [manualTradeAmount, setManualTradeAmount] = useState('10');
  const [isExecutingTrade, setIsExecutingTrade] = useState(false);
  const [lastTradeResult, setLastTradeResult] = useState<{
    type: 'success' | 'error';
    message: string;
    timestamp: Date;
  } | null>(null);

  // Trading options
  const tradeDurations: SelectOption[] = [
    { value: 'S5', label: '5 Seconds' },
    { value: 'S15', label: '15 Seconds' },
    { value: 'S30', label: '30 Seconds' },
    { value: 'M1', label: '1 Minute' },
    { value: 'M5', label: '5 Minutes' },
    { value: 'M15', label: '15 Minutes' },
    { value: 'M30', label: '30 Minutes' },
    { value: 'H1', label: '1 Hour' }
  ];

  const martingaleModes: SelectOption[] = [
    { value: 'none', label: 'None' },
    { value: 'martingale', label: 'Martingale' },
    { value: 'anti-martingale', label: 'Anti-Martingale' },
    { value: 'fibonacci', label: 'Fibonacci' },
    { value: 'dalembert', label: "D'Alembert" }
  ];

  const assets: SelectOption[] = [
    { value: 'EURUSD', label: 'EUR/USD', icon: <DollarSign className="h-4 w-4" /> },
    { value: 'GBPUSD', label: 'GBP/USD', icon: <DollarSign className="h-4 w-4" /> },
    { value: 'USDJPY', label: 'USD/JPY', icon: <DollarSign className="h-4 w-4" /> },
    { value: 'AUDUSD', label: 'AUD/USD', icon: <DollarSign className="h-4 w-4" /> },
    { value: 'BTCUSD', label: 'BTC/USD', icon: <Activity className="h-4 w-4" /> },
    { value: 'ETHUSD', label: 'ETH/USD', icon: <Activity className="h-4 w-4" /> }
  ];

  useEffect(() => {
    // Load saved configuration
    const savedConfig = localStorage.getItem('trading_config');
    if (savedConfig) {
      try {
        const parsed = JSON.parse(savedConfig);
        setTradingConfig(prev => ({ ...prev, ...parsed }));
      } catch (error) {
        console.error('Failed to load trading config:', error);
      }
    }

    // Set up signal generator events
    const handleSignal = (event: any) => {
      if (isTrading && event.data && event.data.confidence >= 70) {
        // Auto-execute high confidence signals
        executeSignalTrade(event.data.action, event.data.confidence);
      }
    };

    signalGenerator.on('signal', handleSignal);

    return () => {
      signalGenerator.off('signal', handleSignal);
    };
  }, [isTrading, signalGenerator]);

  const handleStartTrading = () => {
    if (!webSocketService.isAuthenticated()) {
      setLastTradeResult({
        type: 'error',
        message: 'Please connect to WebSocket first',
        timestamp: new Date()
      });
      return;
    }

    setIsTrading(true);
    signalGenerator.start(selectedAsset);
    
    setLastTradeResult({
      type: 'success',
      message: 'Trading bot started successfully',
      timestamp: new Date()
    });
  };

  const handleStopTrading = () => {
    setIsTrading(false);
    signalGenerator.stop();
    
    setLastTradeResult({
      type: 'success',
      message: 'Trading bot stopped',
      timestamp: new Date()
    });
  };

  const executeSignalTrade = async (action: 'BUY' | 'SELL', confidence: number) => {
    if (!webSocketService.isAuthenticated()) {
      return;
    }

    setIsExecutingTrade(true);

    try {
      const tradeRequest: TradeRequest = {
        asset: selectedAsset,
        action: action.toLowerCase() as 'call' | 'put',
        amount: tradingConfig.tradeAmount,
        duration: tradingConfig.tradeDuration,
        strategy: 'Auto Signal'
      };

      const tradeId = await webSocketService.openTrade(tradeRequest);
      
      setLastTradeResult({
        type: 'success',
        message: `${action} trade executed (ID: ${tradeId.slice(-8)})`,
        timestamp: new Date()
      });

      onTradeExecuted?.({
        id: tradeId,
        ...tradeRequest,
        confidence,
        timestamp: new Date()
      });

    } catch (error) {
      setLastTradeResult({
        type: 'error',
        message: `Trade execution failed: ${error.message}`,
        timestamp: new Date()
      });
    } finally {
      setIsExecutingTrade(false);
    }
  };

  const handleManualTrade = async (action: 'call' | 'put') => {
    if (!webSocketService.isAuthenticated()) {
      setLastTradeResult({
        type: 'error',
        message: 'Please connect to WebSocket first',
        timestamp: new Date()
      });
      return;
    }

    setIsExecutingTrade(true);

    try {
      const tradeRequest: TradeRequest = {
        asset: selectedAsset,
        action,
        amount: parseFloat(manualTradeAmount),
        duration: tradingConfig.tradeDuration,
        strategy: 'Manual Trade'
      };

      const tradeId = await webSocketService.openTrade(tradeRequest);
      
      setLastTradeResult({
        type: 'success',
        message: `Manual ${action.toUpperCase()} trade executed (ID: ${tradeId.slice(-8)})`,
        timestamp: new Date()
      });

      onTradeExecuted?.({
        id: tradeId,
        ...tradeRequest,
        confidence: 0,
        timestamp: new Date()
      });

    } catch (error) {
      setLastTradeResult({
        type: 'error',
        message: `Manual trade failed: ${error.message}`,
        timestamp: new Date()
      });
    } finally {
      setIsExecutingTrade(false);
    }
  };

  const handleConfigChange = (key: keyof TradingConfig, value: any) => {
    const newConfig = { ...tradingConfig, [key]: value };
    setTradingConfig(newConfig);
    
    // Save to localStorage
    localStorage.setItem('trading_config', JSON.stringify(newConfig));
  };

  const validateConfig = () => {
    const errors = [];
    
    if (tradingConfig.tradeAmount > tradingConfig.tradeCapital) {
      errors.push('Trade amount cannot exceed capital');
    }
    
    if (tradingConfig.targetProfit <= 0) {
      errors.push('Target profit must be positive');
    }
    
    if (tradingConfig.stopLoss <= 0) {
      errors.push('Stop loss must be positive');
    }

    return errors;
  };

  const configErrors = validateConfig();
  const canTrade = webSocketService.isAuthenticated() && configErrors.length === 0;

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Trading Controls */}
      <Card 
        variant="trading" 
        title="🎮 Trading Controls" 
        icon={<Target className="h-5 w-5" />}
      >
        <div className="space-y-4">
          {/* Asset Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Select
              label="Trading Asset"
              options={assets}
              value={selectedAsset}
              onChange={setSelectedAsset}
            />
            
            <Input
              label="Manual Trade Amount ($)"
              type="number"
              value={manualTradeAmount}
              onChange={(e) => setManualTradeAmount(e.target.value)}
              prefix="$"
              min="1"
              max="1000"
            />
          </div>

          {/* Main Control Buttons */}
          <div className="flex gap-4">
            <Button
              variant={isTrading ? "danger" : "success"}
              size="lg"
              onClick={isTrading ? handleStopTrading : handleStartTrading}
              disabled={!canTrade}
              icon={isTrading ? <Square className="h-5 w-5" /> : <Play className="h-5 w-5" />}
              className="flex-1"
            >
              {isTrading ? 'STOP AUTO TRADING' : 'START AUTO TRADING'}
            </Button>
          </div>

          {/* Manual Trading Buttons */}
          <div className="grid grid-cols-2 gap-4">
            <Button
              variant="buy"
              onClick={() => handleManualTrade('call')}
              disabled={!canTrade || isExecutingTrade}
              loading={isExecutingTrade}
              icon={<TrendingUp className="h-5 w-5" />}
              size="lg"
            >
              MANUAL BUY
            </Button>
            
            <Button
              variant="sell"
              onClick={() => handleManualTrade('put')}
              disabled={!canTrade || isExecutingTrade}
              loading={isExecutingTrade}
              icon={<TrendingDown className="h-5 w-5" />}
              size="lg"
            >
              MANUAL SELL
            </Button>
          </div>

          {/* Status Display */}
          {lastTradeResult && (
            <div className={`p-3 rounded border ${
              lastTradeResult.type === 'success' 
                ? 'border-[var(--color-success)] bg-[var(--color-success)]/10' 
                : 'border-[var(--color-danger)] bg-[var(--color-danger)]/10'
            }`}>
              <div className="flex items-center gap-2">
                {lastTradeResult.type === 'success' ? (
                  <CheckCircle className="h-4 w-4 text-[var(--color-success)]" />
                ) : (
                  <AlertTriangle className="h-4 w-4 text-[var(--color-danger)]" />
                )}
                <span className="text-sm font-medium">{lastTradeResult.message}</span>
                <span className="text-xs text-[var(--color-text-muted)] ml-auto">
                  {lastTradeResult.timestamp.toLocaleTimeString()}
                </span>
              </div>
            </div>
          )}

          {/* Configuration Errors */}
          {configErrors.length > 0 && (
            <div className="p-3 rounded border border-[var(--color-danger)] bg-[var(--color-danger)]/10">
              <div className="flex items-start gap-2">
                <AlertTriangle className="h-4 w-4 text-[var(--color-danger)] mt-0.5" />
                <div>
                  <div className="text-sm font-medium text-[var(--color-danger)]">Configuration Issues:</div>
                  <ul className="text-xs text-[var(--color-danger)] mt-1 space-y-1">
                    {configErrors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          )}
        </div>
      </Card>

      {/* Trading Configuration */}
      <Card 
        variant="trading" 
        title="⚙️ Trading Configuration" 
        icon={<Settings className="h-5 w-5" />}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            label="Trade Capital ($)"
            type="number"
            value={tradingConfig.tradeCapital.toString()}
            onChange={(e) => handleConfigChange('tradeCapital', parseFloat(e.target.value) || 0)}
            prefix="$"
            min="100"
            max="100000"
          />
          
          <Input
            label="Target Profit ($)"
            type="number"
            value={tradingConfig.targetProfit.toString()}
            onChange={(e) => handleConfigChange('targetProfit', parseFloat(e.target.value) || 0)}
            prefix="$"
            min="1"
          />
          
          <Input
            label="Trade Amount ($)"
            type="number"
            value={tradingConfig.tradeAmount.toString()}
            onChange={(e) => handleConfigChange('tradeAmount', parseFloat(e.target.value) || 0)}
            prefix="$"
            min="1"
            max="1000"
          />
          
          <Select
            label="Trade Duration"
            options={tradeDurations}
            value={tradingConfig.tradeDuration}
            onChange={(value) => handleConfigChange('tradeDuration', value)}
          />
          
          <Input
            label="Stop Loss ($)"
            type="number"
            value={tradingConfig.stopLoss.toString()}
            onChange={(e) => handleConfigChange('stopLoss', parseFloat(e.target.value) || 0)}
            prefix="$"
            min="10"
          />
          
          <Select
            label="Martingale Mode"
            options={martingaleModes}
            value={tradingConfig.martingaleMode}
            onChange={(value) => handleConfigChange('martingaleMode', value)}
          />
          
          <Input
            label="Max Trades/Session"
            type="number"
            value={tradingConfig.maxTradesPerSession.toString()}
            onChange={(e) => handleConfigChange('maxTradesPerSession', parseInt(e.target.value) || 0)}
            min="1"
            max="1000"
          />
          
          <Input
            label="Drawdown Limit (%)"
            type="number"
            value={tradingConfig.drawdownLimit.toString()}
            onChange={(e) => handleConfigChange('drawdownLimit', parseFloat(e.target.value) || 0)}
            suffix="%"
            min="5"
            max="50"
          />
        </div>
      </Card>

      {/* Quick Stats */}
      <Card variant="glass" title="📊 Quick Stats" icon={<Activity className="h-5 w-5" />}>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <div className="text-[var(--color-text-muted)]">Bot Status</div>
            <div className={`font-semibold ${isTrading ? 'text-[var(--color-success)]' : 'text-[var(--color-danger)]'}`}>
              {isTrading ? 'Active' : 'Inactive'}
            </div>
          </div>
          
          <div>
            <div className="text-[var(--color-text-muted)]">Selected Asset</div>
            <div className="font-semibold">{selectedAsset}</div>
          </div>
          
          <div>
            <div className="text-[var(--color-text-muted)]">Trade Duration</div>
            <div className="font-semibold">{tradingConfig.tradeDuration}</div>
          </div>
          
          <div>
            <div className="text-[var(--color-text-muted)]">Risk Level</div>
            <div className={`font-semibold ${
              tradingConfig.drawdownLimit <= 10 ? 'text-[var(--color-success)]' :
              tradingConfig.drawdownLimit <= 20 ? 'text-[var(--color-warning)]' :
              'text-[var(--color-danger)]'
            }`}>
              {tradingConfig.drawdownLimit <= 10 ? 'Low' :
               tradingConfig.drawdownLimit <= 20 ? 'Medium' : 'High'}
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default TradingDashboard;

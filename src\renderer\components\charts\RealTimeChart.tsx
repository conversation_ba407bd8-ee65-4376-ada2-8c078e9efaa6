// 📈 Elite Real-Time Chart Component
import { useState, useEffect, useRef } from 'react';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  Activity,
  Maximize2,
  Minimize2,
  Settings
} from 'lucide-react';
import { OHLC, MarketData } from '../../shared/interfaces/trading';

interface RealTimeChartProps {
  data: OHLC[];
  currentPrice?: number;
  asset: string;
  timeframe: string;
  onTimeframeChange?: (timeframe: string) => void;
  className?: string;
}

interface ChartPoint {
  x: number;
  y: number;
  timestamp: Date;
  price: number;
}

const RealTimeChart: React.FC<RealTimeChartProps> = ({
  data,
  currentPrice,
  asset,
  timeframe,
  onTimeframeChange,
  className = ''
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [chartType, setChartType] = useState<'line' | 'candlestick'>('line');
  const [showVolume, setShowVolume] = useState(false);
  const [priceHistory, setPriceHistory] = useState<ChartPoint[]>([]);

  const timeframes = [
    { value: 'S5', label: '5s' },
    { value: 'S15', label: '15s' },
    { value: 'S30', label: '30s' },
    { value: 'M1', label: '1m' },
    { value: 'M5', label: '5m' },
    { value: 'M15', label: '15m' },
    { value: 'M30', label: '30m' },
    { value: 'H1', label: '1h' }
  ];

  useEffect(() => {
    if (data.length > 0) {
      const points = data.map((candle, index) => ({
        x: index,
        y: candle.close,
        timestamp: candle.timestamp,
        price: candle.close
      }));
      setPriceHistory(points);
    }
  }, [data]);

  useEffect(() => {
    drawChart();
  }, [priceHistory, chartType, showVolume, currentPrice]);

  const drawChart = () => {
    const canvas = canvasRef.current;
    if (!canvas || priceHistory.length === 0) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    const rect = canvas.getBoundingClientRect();
    canvas.width = rect.width * window.devicePixelRatio;
    canvas.height = rect.height * window.devicePixelRatio;
    ctx.scale(window.devicePixelRatio, window.devicePixelRatio);

    const width = rect.width;
    const height = rect.height;
    const padding = 40;
    const chartWidth = width - padding * 2;
    const chartHeight = height - padding * 2;

    // Clear canvas
    ctx.fillStyle = 'var(--color-background)';
    ctx.fillRect(0, 0, width, height);

    // Calculate price range
    const prices = priceHistory.map(p => p.price);
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);
    const priceRange = maxPrice - minPrice;

    if (priceRange === 0) return;

    // Draw grid
    drawGrid(ctx, width, height, padding);

    // Draw price labels
    drawPriceLabels(ctx, minPrice, maxPrice, height, padding);

    // Draw time labels
    drawTimeLabels(ctx, width, padding);

    if (chartType === 'line') {
      drawLineChart(ctx, chartWidth, chartHeight, padding, minPrice, priceRange);
    } else {
      drawCandlestickChart(ctx, chartWidth, chartHeight, padding, minPrice, priceRange);
    }

    // Draw current price line
    if (currentPrice) {
      drawCurrentPriceLine(ctx, currentPrice, chartWidth, chartHeight, padding, minPrice, priceRange);
    }

    // Draw volume if enabled
    if (showVolume && data.length > 0) {
      drawVolumeChart(ctx, chartWidth, chartHeight, padding);
    }
  };

  const drawGrid = (ctx: CanvasRenderingContext2D, width: number, height: number, padding: number) => {
    ctx.strokeStyle = 'var(--color-border)';
    ctx.lineWidth = 1;
    ctx.setLineDash([2, 2]);

    // Horizontal lines
    for (let i = 1; i < 5; i++) {
      const y = padding + (height - padding * 2) * (i / 5);
      ctx.beginPath();
      ctx.moveTo(padding, y);
      ctx.lineTo(width - padding, y);
      ctx.stroke();
    }

    // Vertical lines
    for (let i = 1; i < 6; i++) {
      const x = padding + (width - padding * 2) * (i / 6);
      ctx.beginPath();
      ctx.moveTo(x, padding);
      ctx.lineTo(x, height - padding);
      ctx.stroke();
    }

    ctx.setLineDash([]);
  };

  const drawPriceLabels = (ctx: CanvasRenderingContext2D, minPrice: number, maxPrice: number, height: number, padding: number) => {
    ctx.fillStyle = 'var(--color-text-muted)';
    ctx.font = '12px -apple-system, BlinkMacSystemFont, sans-serif';
    ctx.textAlign = 'right';

    for (let i = 0; i <= 5; i++) {
      const price = minPrice + (maxPrice - minPrice) * (1 - i / 5);
      const y = padding + (height - padding * 2) * (i / 5);
      ctx.fillText(price.toFixed(5), padding - 5, y + 4);
    }
  };

  const drawTimeLabels = (ctx: CanvasRenderingContext2D, width: number, padding: number) => {
    ctx.fillStyle = 'var(--color-text-muted)';
    ctx.font = '12px -apple-system, BlinkMacSystemFont, sans-serif';
    ctx.textAlign = 'center';

    const labelCount = 6;
    for (let i = 0; i < labelCount; i++) {
      const dataIndex = Math.floor((priceHistory.length - 1) * (i / (labelCount - 1)));
      if (dataIndex < priceHistory.length) {
        const point = priceHistory[dataIndex];
        const x = padding + (width - padding * 2) * (i / (labelCount - 1));
        const timeStr = point.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        ctx.fillText(timeStr, x, 380);
      }
    }
  };

  const drawLineChart = (ctx: CanvasRenderingContext2D, chartWidth: number, chartHeight: number, padding: number, minPrice: number, priceRange: number) => {
    if (priceHistory.length < 2) return;

    ctx.strokeStyle = 'var(--color-primary)';
    ctx.lineWidth = 2;
    ctx.beginPath();

    priceHistory.forEach((point, index) => {
      const x = padding + (chartWidth * index) / (priceHistory.length - 1);
      const y = padding + chartHeight - ((point.price - minPrice) / priceRange) * chartHeight;

      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });

    ctx.stroke();

    // Add gradient fill
    ctx.lineTo(padding + chartWidth, padding + chartHeight);
    ctx.lineTo(padding, padding + chartHeight);
    ctx.closePath();

    const gradient = ctx.createLinearGradient(0, padding, 0, padding + chartHeight);
    gradient.addColorStop(0, 'rgba(46, 160, 67, 0.2)');
    gradient.addColorStop(1, 'rgba(46, 160, 67, 0.05)');
    ctx.fillStyle = gradient;
    ctx.fill();
  };

  const drawCandlestickChart = (ctx: CanvasRenderingContext2D, chartWidth: number, chartHeight: number, padding: number, minPrice: number, priceRange: number) => {
    if (data.length === 0) return;

    const candleWidth = Math.max(2, chartWidth / data.length * 0.8);

    data.forEach((candle, index) => {
      const x = padding + (chartWidth * index) / (data.length - 1);
      const openY = padding + chartHeight - ((candle.open - minPrice) / priceRange) * chartHeight;
      const closeY = padding + chartHeight - ((candle.close - minPrice) / priceRange) * chartHeight;
      const highY = padding + chartHeight - ((candle.high - minPrice) / priceRange) * chartHeight;
      const lowY = padding + chartHeight - ((candle.low - minPrice) / priceRange) * chartHeight;

      const isGreen = candle.close >= candle.open;
      ctx.strokeStyle = isGreen ? 'var(--color-success)' : 'var(--color-danger)';
      ctx.fillStyle = isGreen ? 'var(--color-success)' : 'var(--color-danger)';

      // Draw wick
      ctx.lineWidth = 1;
      ctx.beginPath();
      ctx.moveTo(x, highY);
      ctx.lineTo(x, lowY);
      ctx.stroke();

      // Draw body
      const bodyHeight = Math.abs(closeY - openY);
      const bodyY = Math.min(openY, closeY);
      
      if (isGreen) {
        ctx.fillRect(x - candleWidth / 2, bodyY, candleWidth, bodyHeight);
      } else {
        ctx.strokeRect(x - candleWidth / 2, bodyY, candleWidth, bodyHeight);
      }
    });
  };

  const drawCurrentPriceLine = (ctx: CanvasRenderingContext2D, price: number, chartWidth: number, chartHeight: number, padding: number, minPrice: number, priceRange: number) => {
    const y = padding + chartHeight - ((price - minPrice) / priceRange) * chartHeight;
    
    ctx.strokeStyle = 'var(--color-warning)';
    ctx.lineWidth = 2;
    ctx.setLineDash([5, 5]);
    
    ctx.beginPath();
    ctx.moveTo(padding, y);
    ctx.lineTo(padding + chartWidth, y);
    ctx.stroke();
    
    ctx.setLineDash([]);

    // Price label
    ctx.fillStyle = 'var(--color-warning)';
    ctx.font = 'bold 12px -apple-system, BlinkMacSystemFont, sans-serif';
    ctx.textAlign = 'left';
    ctx.fillText(price.toFixed(5), padding + chartWidth + 5, y + 4);
  };

  const drawVolumeChart = (ctx: CanvasRenderingContext2D, chartWidth: number, chartHeight: number, padding: number) => {
    if (data.length === 0) return;

    const volumeHeight = chartHeight * 0.2;
    const volumeY = padding + chartHeight - volumeHeight;
    const maxVolume = Math.max(...data.map(d => d.volume || 0));

    if (maxVolume === 0) return;

    data.forEach((candle, index) => {
      const x = padding + (chartWidth * index) / (data.length - 1);
      const volume = candle.volume || 0;
      const barHeight = (volume / maxVolume) * volumeHeight;
      
      ctx.fillStyle = candle.close >= candle.open ? 'rgba(46, 160, 67, 0.6)' : 'rgba(248, 81, 73, 0.6)';
      ctx.fillRect(x - 2, volumeY + volumeHeight - barHeight, 4, barHeight);
    });
  };

  return (
    <Card 
      variant="trading" 
      className={`${className} ${isFullscreen ? 'fixed inset-4 z-50' : ''}`}
      title={`📈 ${asset} Chart`}
      icon={<BarChart3 className="h-5 w-5" />}
      actions={
        <div className="flex items-center gap-2">
          {/* Timeframe Selector */}
          <div className="flex gap-1">
            {timeframes.map(tf => (
              <Button
                key={tf.value}
                variant={timeframe === tf.value ? 'primary' : 'ghost'}
                size="sm"
                onClick={() => onTimeframeChange?.(tf.value)}
                className="px-2 py-1 text-xs"
              >
                {tf.label}
              </Button>
            ))}
          </div>

          {/* Chart Type Toggle */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setChartType(chartType === 'line' ? 'candlestick' : 'line')}
          >
            {chartType === 'line' ? 'Line' : 'Candles'}
          </Button>

          {/* Volume Toggle */}
          <Button
            variant={showVolume ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setShowVolume(!showVolume)}
          >
            Volume
          </Button>

          {/* Fullscreen Toggle */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsFullscreen(!isFullscreen)}
            icon={isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
          />
        </div>
      }
    >
      <div className="relative">
        <canvas
          ref={canvasRef}
          className="w-full h-96 border border-[var(--color-border)] rounded"
          style={{ background: 'var(--color-surface)' }}
        />
        
        {priceHistory.length === 0 && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <Activity className="h-12 w-12 text-[var(--color-text-muted)] mx-auto mb-4" />
              <p className="text-[var(--color-text-muted)]">Waiting for chart data...</p>
            </div>
          </div>
        )}

        {/* Chart Stats Overlay */}
        {priceHistory.length > 0 && (
          <div className="absolute top-4 left-4 bg-[var(--color-surface)]/90 backdrop-blur-sm rounded p-3 border border-[var(--color-border)]">
            <div className="text-sm space-y-1">
              <div className="flex items-center gap-2">
                <span className="text-[var(--color-text-muted)]">Current:</span>
                <span className="font-semibold">{currentPrice?.toFixed(5) || 'N/A'}</span>
              </div>
              {data.length > 0 && (
                <>
                  <div className="flex items-center gap-2">
                    <span className="text-[var(--color-text-muted)]">High:</span>
                    <span className="text-[var(--color-success)]">{Math.max(...data.map(d => d.high)).toFixed(5)}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-[var(--color-text-muted)]">Low:</span>
                    <span className="text-[var(--color-danger)]">{Math.min(...data.map(d => d.low)).toFixed(5)}</span>
                  </div>
                </>
              )}
              <div className="flex items-center gap-2">
                <span className="text-[var(--color-text-muted)]">Points:</span>
                <span>{priceHistory.length}</span>
              </div>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
};

export default RealTimeChart;

// 🧪 Elite Database Service Test Suite
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { DatabaseService } from '../renderer/services/DatabaseService';
import { TradingSession } from '../shared/interfaces/trading';

// Mock localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {};

  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value.toString();
    },
    removeItem: (key: string) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    }
  };
})();

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

describe('DatabaseService', () => {
  let dbService: DatabaseService;

  beforeEach(() => {
    localStorageMock.clear();
    dbService = new DatabaseService({
      maxSessions: 10,
      maxTradesPerSession: 100,
      autoCleanup: false
    });
  });

  afterEach(() => {
    localStorageMock.clear();
  });

  describe('Initialization', () => {
    it('should initialize with empty data structures', async () => {
      const sessions = await dbService.getAllSessions();
      expect(sessions).toHaveLength(0);
    });

    it('should create required localStorage keys', () => {
      expect(localStorage.getItem('trading_sessions')).toBe('[]');
      expect(localStorage.getItem('trading_trades')).toBe('[]');
      expect(localStorage.getItem('trading_performance')).toBe('[]');
      expect(localStorage.getItem('trading_signals')).toBe('[]');
      expect(localStorage.getItem('trading_strategies')).toBe('[]');
    });
  });

  describe('Session Management', () => {
    it('should create a new session', async () => {
      const sessionData = {
        startTime: new Date(),
        initialBalance: 10000,
        currentBalance: 10000,
        totalTrades: 0,
        winningTrades: 0,
        losingTrades: 0,
        winRate: 0,
        maxDrawdown: 0,
        isActive: true
      };

      const sessionId = await dbService.createSession(sessionData);
      expect(sessionId).toBeDefined();
      expect(sessionId).toMatch(/^session_/);

      const session = await dbService.getSession(sessionId);
      expect(session).toBeDefined();
      expect(session?.initialBalance).toBe(10000);
      expect(session?.isActive).toBe(true);
    });

    it('should update an existing session', async () => {
      const sessionId = await dbService.createSession({
        startTime: new Date(),
        initialBalance: 10000,
        currentBalance: 10000,
        totalTrades: 0,
        winningTrades: 0,
        losingTrades: 0,
        winRate: 0,
        maxDrawdown: 0,
        isActive: true
      });

      const updated = await dbService.updateSession(sessionId, {
        currentBalance: 10500,
        totalTrades: 5,
        winRate: 80
      });

      expect(updated).toBe(true);

      const session = await dbService.getSession(sessionId);
      expect(session?.currentBalance).toBe(10500);
      expect(session?.totalTrades).toBe(5);
      expect(session?.winRate).toBe(80);
    });

    it('should delete a session and its trades', async () => {
      const sessionId = await dbService.createSession({
        startTime: new Date(),
        initialBalance: 10000,
        currentBalance: 10000,
        totalTrades: 0,
        winningTrades: 0,
        losingTrades: 0,
        winRate: 0,
        maxDrawdown: 0,
        isActive: true
      });

      // Add a trade to the session
      await dbService.createTrade({
        sessionId,
        timestamp: new Date(),
        entryTime: new Date(),
        asset: 'EURUSD',
        action: 'BUY',
        amount: 100,
        result: 'win',
        profit: 80,
        strategy: 'Test Strategy'
      });

      const deleted = await dbService.deleteSession(sessionId);
      expect(deleted).toBe(true);

      const session = await dbService.getSession(sessionId);
      expect(session).toBeNull();

      const trades = await dbService.getTradesBySession(sessionId);
      expect(trades).toHaveLength(0);
    });

    it('should get active sessions', async () => {
      // Create active session
      await dbService.createSession({
        startTime: new Date(),
        initialBalance: 10000,
        currentBalance: 10000,
        totalTrades: 0,
        winningTrades: 0,
        losingTrades: 0,
        winRate: 0,
        maxDrawdown: 0,
        isActive: true
      });

      // Create inactive session
      await dbService.createSession({
        startTime: new Date(),
        initialBalance: 10000,
        currentBalance: 10000,
        totalTrades: 0,
        winningTrades: 0,
        losingTrades: 0,
        winRate: 0,
        maxDrawdown: 0,
        isActive: false
      });

      const activeSessions = await dbService.getActiveSessions();
      expect(activeSessions).toHaveLength(1);
      expect(activeSessions[0].isActive).toBe(true);
    });
  });

  describe('Trade Management', () => {
    let sessionId: string;

    beforeEach(async () => {
      sessionId = await dbService.createSession({
        startTime: new Date(),
        initialBalance: 10000,
        currentBalance: 10000,
        totalTrades: 0,
        winningTrades: 0,
        losingTrades: 0,
        winRate: 0,
        maxDrawdown: 0,
        isActive: true
      });
    });

    it('should create a new trade', async () => {
      const tradeData = {
        sessionId,
        timestamp: new Date(),
        entryTime: new Date(),
        asset: 'EURUSD',
        action: 'BUY' as const,
        amount: 100,
        result: 'win' as const,
        profit: 80,
        strategy: 'RSI Strategy'
      };

      const tradeId = await dbService.createTrade(tradeData);
      expect(tradeId).toBeDefined();
      expect(tradeId).toMatch(/^trade_/);

      const trade = await dbService.getTrade(tradeId);
      expect(trade).toBeDefined();
      expect(trade?.asset).toBe('EURUSD');
      expect(trade?.action).toBe('BUY');
      expect(trade?.profit).toBe(80);
    });

    it('should update an existing trade', async () => {
      const tradeId = await dbService.createTrade({
        sessionId,
        timestamp: new Date(),
        entryTime: new Date(),
        asset: 'EURUSD',
        action: 'BUY',
        amount: 100,
        result: 'pending',
        strategy: 'Test Strategy'
      });

      const updated = await dbService.updateTrade(tradeId, {
        result: 'win',
        profit: 85,
        exitTime: new Date()
      });

      expect(updated).toBe(true);

      const trade = await dbService.getTrade(tradeId);
      expect(trade?.result).toBe('win');
      expect(trade?.profit).toBe(85);
      expect(trade?.exitTime).toBeDefined();
    });

    it('should get trades by session', async () => {
      // Create trades for the session
      await dbService.createTrade({
        sessionId,
        timestamp: new Date(),
        entryTime: new Date(),
        asset: 'EURUSD',
        action: 'BUY',
        amount: 100,
        result: 'win',
        profit: 80,
        strategy: 'Strategy 1'
      });

      await dbService.createTrade({
        sessionId,
        timestamp: new Date(),
        entryTime: new Date(),
        asset: 'GBPUSD',
        action: 'SELL',
        amount: 150,
        result: 'loss',
        profit: -120,
        strategy: 'Strategy 2'
      });

      const trades = await dbService.getTradesBySession(sessionId);
      expect(trades).toHaveLength(2);
      expect(trades[0].sessionId).toBe(sessionId);
      expect(trades[1].sessionId).toBe(sessionId);
    });

    it('should limit trades per session', async () => {
      // Create service with low limit for testing
      const limitedDbService = new DatabaseService({
        maxSessions: 10,
        maxTradesPerSession: 3,
        autoCleanup: false
      });

      const testSessionId = await limitedDbService.createSession({
        startTime: new Date(),
        initialBalance: 10000,
        currentBalance: 10000,
        totalTrades: 0,
        winningTrades: 0,
        losingTrades: 0,
        winRate: 0,
        maxDrawdown: 0,
        isActive: true
      });

      // Add more trades than the limit
      for (let i = 0; i < 5; i++) {
        await limitedDbService.createTrade({
          sessionId: testSessionId,
          timestamp: new Date(Date.now() + i * 1000),
          entryTime: new Date(Date.now() + i * 1000),
          asset: 'EURUSD',
          action: 'BUY',
          amount: 100,
          result: 'win',
          profit: 80,
          strategy: `Strategy ${i}`
        });
      }

      const trades = await limitedDbService.getTradesBySession(testSessionId);
      expect(trades.length).toBeLessThanOrEqual(3);
    });
  });

  describe('Performance Metrics', () => {
    let sessionId: string;

    beforeEach(async () => {
      sessionId = await dbService.createSession({
        startTime: new Date(),
        initialBalance: 10000,
        currentBalance: 10000,
        totalTrades: 0,
        winningTrades: 0,
        losingTrades: 0,
        winRate: 0,
        maxDrawdown: 0,
        isActive: true
      });
    });

    it('should save performance metrics', async () => {
      const metrics = {
        balance: 10500,
        profitLoss: 500,
        winRate: 75,
        totalTrades: 10,
        activeTrades: 2,
        dailyPnL: 500,
        weeklyPnL: 1200,
        monthlyPnL: 3500,
        maxDrawdown: 5,
        currentDrawdown: 2
      };

      await dbService.savePerformanceMetrics(sessionId, metrics);

      const savedMetrics = await dbService.getLatestPerformanceMetrics(sessionId);
      expect(savedMetrics).toBeDefined();
      expect(savedMetrics?.balance).toBe(10500);
      expect(savedMetrics?.winRate).toBe(75);
    });

    it('should get performance metrics history', async () => {
      const metrics1 = {
        balance: 10200,
        profitLoss: 200,
        winRate: 70,
        totalTrades: 5,
        activeTrades: 1,
        dailyPnL: 200,
        weeklyPnL: 800,
        monthlyPnL: 2000,
        maxDrawdown: 3,
        currentDrawdown: 1
      };

      const metrics2 = {
        balance: 10500,
        profitLoss: 500,
        winRate: 75,
        totalTrades: 10,
        activeTrades: 2,
        dailyPnL: 500,
        weeklyPnL: 1200,
        monthlyPnL: 3500,
        maxDrawdown: 5,
        currentDrawdown: 2
      };

      await dbService.savePerformanceMetrics(sessionId, metrics1);
      await dbService.savePerformanceMetrics(sessionId, metrics2);

      const metricsHistory = await dbService.getPerformanceMetrics(sessionId);
      expect(metricsHistory).toHaveLength(2);
      expect(metricsHistory[1].balance).toBe(10500);
    });
  });

  describe('Signal Management', () => {
    it('should save signals', async () => {
      const signal = {
        asset: 'EURUSD',
        action: 'BUY',
        confidence: 85,
        strategy: 'RSI + MACD',
        indicators: {
          rsi: 25,
          macd: 0.0015
        }
      };

      const signalId = await dbService.saveSignal(signal);
      expect(signalId).toBeDefined();
      expect(signalId).toMatch(/^signal_/);
    });

    it('should get recent signals', async () => {
      // Save multiple signals
      for (let i = 0; i < 5; i++) {
        await dbService.saveSignal({
          asset: 'EURUSD',
          action: i % 2 === 0 ? 'BUY' : 'SELL',
          confidence: 70 + i * 5,
          strategy: `Strategy ${i}`
        });
      }

      const signals = await dbService.getSignals(3);
      expect(signals).toHaveLength(3);
    });

    it('should limit signal storage', async () => {
      // This test would require mocking the limit, but demonstrates the concept
      const signals = await dbService.getSignals();
      expect(signals).toBeInstanceOf(Array);
    });
  });

  describe('Strategy Performance', () => {
    it('should save strategy performance', async () => {
      const performance = {
        signalCount: 25,
        winRate: 72,
        avgConfidence: 78,
        profitFactor: 1.8
      };

      await dbService.saveStrategyPerformance('RSI', performance);

      const savedPerformance = await dbService.getStrategyPerformance('RSI');
      expect(savedPerformance).toBeDefined();
      expect(savedPerformance.winRate).toBe(72);
    });

    it('should update existing strategy performance', async () => {
      const initialPerformance = {
        signalCount: 20,
        winRate: 70,
        avgConfidence: 75
      };

      const updatedPerformance = {
        signalCount: 30,
        winRate: 75,
        avgConfidence: 80
      };

      await dbService.saveStrategyPerformance('MACD', initialPerformance);
      await dbService.saveStrategyPerformance('MACD', updatedPerformance);

      const savedPerformance = await dbService.getStrategyPerformance('MACD');
      expect(savedPerformance.signalCount).toBe(30);
      expect(savedPerformance.winRate).toBe(75);
    });

    it('should get all strategy performance', async () => {
      await dbService.saveStrategyPerformance('RSI', { winRate: 70 });
      await dbService.saveStrategyPerformance('MACD', { winRate: 75 });
      await dbService.saveStrategyPerformance('Bollinger', { winRate: 68 });

      const allPerformance = await dbService.getAllStrategyPerformance();
      expect(allPerformance).toHaveLength(3);
    });
  });

  describe('Analytics', () => {
    it('should get session statistics', async () => {
      // Create test sessions
      await dbService.createSession({
        startTime: new Date(),
        initialBalance: 10000,
        currentBalance: 10000,
        totalTrades: 0,
        winningTrades: 0,
        losingTrades: 0,
        winRate: 0,
        maxDrawdown: 0,
        isActive: true
      });

      await dbService.createSession({
        startTime: new Date(),
        initialBalance: 10000,
        currentBalance: 10000,
        totalTrades: 0,
        winningTrades: 0,
        losingTrades: 0,
        winRate: 0,
        maxDrawdown: 0,
        isActive: false
      });

      const stats = await dbService.getSessionStatistics();
      expect(stats.totalSessions).toBe(2);
      expect(stats.activeSessions).toBe(1);
      expect(stats.totalTrades).toBe(0);
      expect(stats.avgTradesPerSession).toBe(0);
    });
  });

  describe('Data Export/Import', () => {
    it('should export all data', async () => {
      // Create test data
      const sessionId = await dbService.createSession({
        startTime: new Date(),
        initialBalance: 10000,
        currentBalance: 10000,
        totalTrades: 0,
        winningTrades: 0,
        losingTrades: 0,
        winRate: 0,
        maxDrawdown: 0,
        isActive: true
      });

      await dbService.createTrade({
        sessionId,
        timestamp: new Date(),
        entryTime: new Date(),
        asset: 'EURUSD',
        action: 'BUY',
        amount: 100,
        result: 'win',
        profit: 80,
        strategy: 'Test Strategy'
      });

      const exportedData = await dbService.exportData();
      expect(exportedData.sessions).toHaveLength(1);
      expect(exportedData.trades).toHaveLength(1);
      expect(exportedData.performance).toBeInstanceOf(Array);
      expect(exportedData.signals).toBeInstanceOf(Array);
      expect(exportedData.strategies).toBeInstanceOf(Array);
    });

    it('should import data successfully', async () => {
      const importData = {
        sessions: [{
          id: 'test-session-1',
          startTime: new Date().toISOString(),
          initialBalance: 10000,
          currentBalance: 10500,
          totalTrades: 5,
          winningTrades: 4,
          losingTrades: 1,
          winRate: 80,
          maxDrawdown: 2,
          isActive: false
        }],
        trades: [{
          id: 'test-trade-1',
          sessionId: 'test-session-1',
          timestamp: new Date().toISOString(),
          entryTime: new Date().toISOString(),
          asset: 'EURUSD',
          action: 'BUY',
          amount: 100,
          result: 'win',
          profit: 80,
          strategy: 'Imported Strategy'
        }]
      };

      const success = await dbService.importData(importData);
      expect(success).toBe(true);

      const sessions = await dbService.getAllSessions();
      expect(sessions.length).toBeGreaterThan(0);
    });

    it('should clear all data', async () => {
      // Create some data first
      await dbService.createSession({
        startTime: new Date(),
        initialBalance: 10000,
        currentBalance: 10000,
        totalTrades: 0,
        winningTrades: 0,
        losingTrades: 0,
        winRate: 0,
        maxDrawdown: 0,
        isActive: true
      });

      await dbService.clearAllData();

      const sessions = await dbService.getAllSessions();
      expect(sessions).toHaveLength(0);
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid session ID gracefully', async () => {
      const session = await dbService.getSession('invalid-id');
      expect(session).toBeNull();

      const updated = await dbService.updateSession('invalid-id', { winRate: 50 });
      expect(updated).toBe(false);
    });

    it('should handle invalid trade ID gracefully', async () => {
      const trade = await dbService.getTrade('invalid-id');
      expect(trade).toBeNull();

      const updated = await dbService.updateTrade('invalid-id', { result: 'win' });
      expect(updated).toBe(false);
    });

    it('should handle corrupted localStorage data', () => {
      // Corrupt the sessions data
      localStorage.setItem('trading_sessions', 'invalid-json');

      // Should not throw and should return empty array
      const newDbService = new DatabaseService();
      expect(() => newDbService).not.toThrow();
    });
  });
});

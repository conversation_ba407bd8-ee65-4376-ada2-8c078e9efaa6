// 🎯 Elite Signal Generator Service
import { EventEmitter } from 'events';
import TradingEngine, { CombinedSignal, StrategyResult } from './TradingEngine';
import { OHLC, MarketData, SignalData } from '../../shared/interfaces/trading';
import { EMOJI_INDICATORS } from '../../shared/constants/trading';

export interface SignalEvent {
  type: 'signal' | 'strategy_update' | 'market_update';
  data: any;
  timestamp: Date;
}

export interface SignalHistory {
  signal: CombinedSignal;
  executed: boolean;
  result?: 'win' | 'loss' | 'pending';
  profit?: number;
}

export class SignalGenerator extends EventEmitter {
  private tradingEngine: TradingEngine;
  private signalHistory: SignalHistory[] = [];
  private isActive: boolean = false;
  private lastSignalTime: Date | null = null;
  private minSignalInterval: number = 30000; // 30 seconds minimum between signals
  private currentAsset: string = '';
  private signalCount: number = 0;

  constructor() {
    super();
    this.tradingEngine = new TradingEngine();
    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    // Set up any additional event handlers if needed
  }

  // 🚀 Start/Stop Signal Generation
  start(asset: string): void {
    this.isActive = true;
    this.currentAsset = asset;
    this.tradingEngine.setActive(true);
    
    this.emit('status', {
      type: 'started',
      asset,
      timestamp: new Date(),
      message: `${EMOJI_INDICATORS.ROCKET} Signal generation started for ${asset}`
    });
  }

  stop(): void {
    this.isActive = false;
    this.tradingEngine.setActive(false);
    
    this.emit('status', {
      type: 'stopped',
      timestamp: new Date(),
      message: `${EMOJI_INDICATORS.GEAR} Signal generation stopped`
    });
  }

  // 📊 Market Data Processing
  processMarketData(data: MarketData): void {
    if (!this.isActive) return;

    // Update current price
    this.tradingEngine.updateCurrentPrice(data.price);

    this.emit('market_update', {
      type: 'price_update',
      data,
      timestamp: new Date()
    });
  }

  processOHLCData(candle: OHLC): void {
    if (!this.isActive) return;

    // Update trading engine with new candle
    this.tradingEngine.updateMarketData(candle);

    // Generate signals if enough data is available
    if (this.tradingEngine.getMarketDataLength() >= 50) {
      this.generateAndEmitSignal();
    }

    this.emit('market_update', {
      type: 'candle_update',
      data: candle,
      timestamp: new Date()
    });
  }

  // 🎯 Signal Generation
  private generateAndEmitSignal(): void {
    // Check minimum interval between signals
    if (this.lastSignalTime && 
        Date.now() - this.lastSignalTime.getTime() < this.minSignalInterval) {
      return;
    }

    const combinedSignal = this.tradingEngine.generateSignals();

    // Only emit signals with sufficient confidence
    if (combinedSignal.confidence >= 60 && combinedSignal.action !== 'neutral') {
      this.signalCount++;
      this.lastSignalTime = new Date();

      // Add to history
      this.signalHistory.push({
        signal: combinedSignal,
        executed: false
      });

      // Keep only last 100 signals
      if (this.signalHistory.length > 100) {
        this.signalHistory = this.signalHistory.slice(-100);
      }

      // Create signal data
      const signalData: SignalData = {
        timestamp: combinedSignal.timestamp,
        asset: this.currentAsset,
        action: combinedSignal.action.toUpperCase() as any,
        amount: 0, // Will be set by trading logic
        strategy: this.getTopStrategies(combinedSignal.strategies),
        confidence: combinedSignal.confidence,
        indicators: this.extractIndicators(combinedSignal.strategies)
      };

      // Emit signal event
      this.emit('signal', {
        type: 'trading_signal',
        data: signalData,
        combinedSignal,
        timestamp: new Date(),
        message: this.formatSignalMessage(combinedSignal)
      });

      // Emit individual strategy updates
      combinedSignal.strategies.forEach(strategy => {
        this.emit('strategy_update', {
          type: 'strategy_signal',
          data: strategy,
          timestamp: new Date()
        });
      });
    }
  }

  // 📈 Manual Signal Generation (for testing)
  generateManualSignal(): CombinedSignal | null {
    if (!this.isActive || this.tradingEngine.getMarketDataLength() < 50) {
      return null;
    }

    return this.tradingEngine.generateSignals();
  }

  // 🎛️ Strategy Management
  enableStrategy(strategyName: string): void {
    this.tradingEngine.enableStrategy(strategyName);
    
    this.emit('strategy_update', {
      type: 'strategy_enabled',
      data: { strategy: strategyName },
      timestamp: new Date(),
      message: `${EMOJI_INDICATORS.SUCCESS} Strategy ${strategyName} enabled`
    });
  }

  disableStrategy(strategyName: string): void {
    this.tradingEngine.disableStrategy(strategyName);
    
    this.emit('strategy_update', {
      type: 'strategy_disabled',
      data: { strategy: strategyName },
      timestamp: new Date(),
      message: `${EMOJI_INDICATORS.WARNING} Strategy ${strategyName} disabled`
    });
  }

  updateStrategyWeight(strategyName: string, weight: number): void {
    this.tradingEngine.updateStrategyWeight(strategyName, weight);
    
    this.emit('strategy_update', {
      type: 'strategy_weight_updated',
      data: { strategy: strategyName, weight },
      timestamp: new Date(),
      message: `${EMOJI_INDICATORS.GEAR} Strategy ${strategyName} weight updated to ${weight}`
    });
  }

  updateStrategyParameters(strategyName: string, parameters: Record<string, number>): void {
    this.tradingEngine.updateStrategyParameters(strategyName, parameters);
    
    this.emit('strategy_update', {
      type: 'strategy_parameters_updated',
      data: { strategy: strategyName, parameters },
      timestamp: new Date(),
      message: `${EMOJI_INDICATORS.GEAR} Strategy ${strategyName} parameters updated`
    });
  }

  // 📊 Analytics and Reporting
  getSignalHistory(): SignalHistory[] {
    return [...this.signalHistory];
  }

  getSignalStats(): {
    totalSignals: number;
    winRate: number;
    avgConfidence: number;
    lastSignalTime: Date | null;
    activeStrategies: number;
  } {
    const executedSignals = this.signalHistory.filter(h => h.executed && h.result !== 'pending');
    const winningSignals = executedSignals.filter(h => h.result === 'win');
    const winRate = executedSignals.length > 0 ? (winningSignals.length / executedSignals.length) * 100 : 0;
    
    const avgConfidence = this.signalHistory.length > 0 
      ? this.signalHistory.reduce((sum, h) => sum + h.signal.confidence, 0) / this.signalHistory.length
      : 0;

    const activeStrategies = this.tradingEngine.getStrategies().filter(s => s.enabled).length;

    return {
      totalSignals: this.signalCount,
      winRate,
      avgConfidence,
      lastSignalTime: this.lastSignalTime,
      activeStrategies
    };
  }

  getStrategyPerformance(): Array<{
    name: string;
    enabled: boolean;
    weight: number;
    signalCount: number;
    avgConfidence: number;
  }> {
    const strategies = this.tradingEngine.getStrategies();
    
    return strategies.map(strategy => {
      const strategySignals = this.signalHistory.filter(h => 
        h.signal.strategies.some(s => s.strategy === strategy.name)
      );

      const avgConfidence = strategySignals.length > 0
        ? strategySignals.reduce((sum, h) => {
            const strategyResult = h.signal.strategies.find(s => s.strategy === strategy.name);
            return sum + (strategyResult?.confidence || 0);
          }, 0) / strategySignals.length
        : 0;

      return {
        name: strategy.name,
        enabled: strategy.enabled,
        weight: strategy.weight,
        signalCount: strategySignals.length,
        avgConfidence
      };
    });
  }

  // 🔧 Utility Methods
  private getTopStrategies(strategies: StrategyResult[]): string {
    return strategies
      .filter(s => s.signal !== 'neutral')
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, 3)
      .map(s => s.strategy.split(' ')[0])
      .join(', ');
  }

  private extractIndicators(strategies: StrategyResult[]): Record<string, number> {
    const indicators: Record<string, number> = {};
    
    strategies.forEach(strategy => {
      Object.entries(strategy.indicators).forEach(([key, value]) => {
        indicators[`${strategy.strategy.split(' ')[0]}_${key}`] = value;
      });
    });

    return indicators;
  }

  private formatSignalMessage(signal: CombinedSignal): string {
    const emoji = signal.action === 'buy' ? EMOJI_INDICATORS.TRADE_UP : EMOJI_INDICATORS.TRADE_DOWN;
    const action = signal.action.toUpperCase();
    const confidence = signal.confidence.toFixed(1);
    const topStrategies = this.getTopStrategies(signal.strategies);

    return `${emoji} ${action} Signal | Confidence: ${confidence}% | Strategies: ${topStrategies}`;
  }

  // 📝 Signal Execution Tracking
  markSignalExecuted(signalIndex: number, executed: boolean = true): void {
    if (signalIndex >= 0 && signalIndex < this.signalHistory.length) {
      this.signalHistory[signalIndex].executed = executed;
    }
  }

  updateSignalResult(signalIndex: number, result: 'win' | 'loss', profit?: number): void {
    if (signalIndex >= 0 && signalIndex < this.signalHistory.length) {
      this.signalHistory[signalIndex].result = result;
      if (profit !== undefined) {
        this.signalHistory[signalIndex].profit = profit;
      }
    }
  }

  // ⚙️ Configuration
  setMinSignalInterval(intervalMs: number): void {
    this.minSignalInterval = Math.max(1000, intervalMs); // Minimum 1 second
  }

  getCurrentAsset(): string {
    return this.currentAsset;
  }

  isGeneratorActive(): boolean {
    return this.isActive;
  }

  getEngineStatus(): {
    active: boolean;
    dataPoints: number;
    currentPrice: number;
    strategies: number;
    enabledStrategies: number;
  } {
    const strategies = this.tradingEngine.getStrategies();
    
    return {
      active: this.isActive,
      dataPoints: this.tradingEngine.getMarketDataLength(),
      currentPrice: this.tradingEngine.getCurrentPrice(),
      strategies: strategies.length,
      enabledStrategies: strategies.filter(s => s.enabled).length
    };
  }

  // 🧹 Cleanup
  cleanup(): void {
    this.stop();
    this.removeAllListeners();
    this.signalHistory = [];
    this.signalCount = 0;
    this.lastSignalTime = null;
  }
}

export default SignalGenerator;

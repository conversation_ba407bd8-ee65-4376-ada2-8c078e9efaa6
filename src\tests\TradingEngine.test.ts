// 🧪 Elite Trading Engine Test Suite
import { describe, it, expect, beforeEach } from 'vitest';
import TradingEngine from '../renderer/services/TradingEngine';
import { OHLC } from '../shared/interfaces/trading';

describe('TradingEngine', () => {
  let engine: TradingEngine;
  let mockOHLCData: OHLC[];

  beforeEach(() => {
    engine = new TradingEngine();
    
    // Create mock OHLC data for testing
    mockOHLCData = [];
    const basePrice = 1.0850;
    const now = new Date();
    
    for (let i = 0; i < 100; i++) {
      const timestamp = new Date(now.getTime() - (100 - i) * 60000); // 1 minute intervals
      const open = basePrice + (Math.random() - 0.5) * 0.01;
      const close = open + (Math.random() - 0.5) * 0.005;
      const high = Math.max(open, close) + Math.random() * 0.003;
      const low = Math.min(open, close) - Math.random() * 0.003;
      
      mockOHLCData.push({
        timestamp,
        open,
        high,
        low,
        close,
        volume: Math.floor(Math.random() * 1000) + 100
      });
    }
  });

  describe('Initialization', () => {
    it('should initialize with default strategies', () => {
      const strategies = engine.getStrategies();
      expect(strategies).toHaveLength(8);
      expect(strategies.every(s => s.enabled)).toBe(true);
    });

    it('should start inactive', () => {
      expect(engine.isEngineActive()).toBe(false);
      expect(engine.getMarketDataLength()).toBe(0);
    });
  });

  describe('Market Data Processing', () => {
    it('should update market data correctly', () => {
      const candle = mockOHLCData[0];
      engine.updateMarketData(candle);
      
      expect(engine.getMarketDataLength()).toBe(1);
      expect(engine.getCurrentPrice()).toBe(candle.close);
    });

    it('should limit market data to 200 candles', () => {
      // Add more than 200 candles
      for (let i = 0; i < 250; i++) {
        engine.updateMarketData(mockOHLCData[i % mockOHLCData.length]);
      }
      
      expect(engine.getMarketDataLength()).toBe(200);
    });

    it('should update current price independently', () => {
      const newPrice = 1.0900;
      engine.updateCurrentPrice(newPrice);
      expect(engine.getCurrentPrice()).toBe(newPrice);
    });
  });

  describe('Signal Generation', () => {
    beforeEach(() => {
      // Add sufficient data for signal generation
      mockOHLCData.forEach(candle => {
        engine.updateMarketData(candle);
      });
      engine.setActive(true);
    });

    it('should generate signals with sufficient data', () => {
      const signal = engine.generateSignals();
      
      expect(signal).toBeDefined();
      expect(signal.action).toMatch(/^(buy|sell|neutral)$/);
      expect(signal.confidence).toBeGreaterThanOrEqual(0);
      expect(signal.confidence).toBeLessThanOrEqual(100);
      expect(signal.strategies).toBeInstanceOf(Array);
      expect(signal.timestamp).toBeInstanceOf(Date);
    });

    it('should return neutral signal with insufficient data', () => {
      const newEngine = new TradingEngine();
      const signal = newEngine.generateSignals();
      
      expect(signal.action).toBe('neutral');
      expect(signal.confidence).toBe(0);
      expect(signal.strategies).toHaveLength(0);
    });

    it('should include strategy results in signal', () => {
      const signal = engine.generateSignals();
      
      expect(signal.strategies.length).toBeGreaterThan(0);
      signal.strategies.forEach(strategy => {
        expect(strategy.strategy).toBeDefined();
        expect(strategy.signal).toMatch(/^(buy|sell|neutral)$/);
        expect(strategy.strength).toBeGreaterThanOrEqual(0);
        expect(strategy.confidence).toBeGreaterThanOrEqual(0);
        expect(strategy.confidence).toBeLessThanOrEqual(100);
        expect(strategy.indicators).toBeDefined();
        expect(strategy.timestamp).toBeInstanceOf(Date);
      });
    });
  });

  describe('Strategy Management', () => {
    it('should enable and disable strategies', () => {
      engine.disableStrategy('RSI');
      let strategies = engine.getStrategies();
      const rsiStrategy = strategies.find(s => s.name.includes('RSI'));
      expect(rsiStrategy?.enabled).toBe(false);

      engine.enableStrategy('RSI');
      strategies = engine.getStrategies();
      const enabledRsiStrategy = strategies.find(s => s.name.includes('RSI'));
      expect(enabledRsiStrategy?.enabled).toBe(true);
    });

    it('should update strategy weights', () => {
      const newWeight = 0.25;
      engine.updateStrategyWeight('RSI', newWeight);
      
      const strategies = engine.getStrategies();
      const rsiStrategy = strategies.find(s => s.name.includes('RSI'));
      expect(rsiStrategy?.weight).toBe(newWeight);
    });

    it('should clamp strategy weights between 0 and 1', () => {
      engine.updateStrategyWeight('RSI', 1.5);
      let strategies = engine.getStrategies();
      let rsiStrategy = strategies.find(s => s.name.includes('RSI'));
      expect(rsiStrategy?.weight).toBe(1);

      engine.updateStrategyWeight('RSI', -0.5);
      strategies = engine.getStrategies();
      rsiStrategy = strategies.find(s => s.name.includes('RSI'));
      expect(rsiStrategy?.weight).toBe(0);
    });

    it('should update strategy parameters', () => {
      const newParams = { period: 21, oversoldLevel: 25 };
      engine.updateStrategyParameters('RSI', newParams);
      
      const strategy = engine.getStrategyStatus('RSI');
      expect(strategy?.parameters.period).toBe(21);
      expect(strategy?.parameters.oversoldLevel).toBe(25);
    });
  });

  describe('Engine State Management', () => {
    it('should activate and deactivate engine', () => {
      engine.setActive(true);
      expect(engine.isEngineActive()).toBe(true);

      engine.setActive(false);
      expect(engine.isEngineActive()).toBe(false);
    });

    it('should return strategy status', () => {
      const status = engine.getStrategyStatus('RSI');
      expect(status).toBeDefined();
      expect(status?.name).toContain('RSI');
      expect(status?.enabled).toBe(true);
      expect(status?.weight).toBeGreaterThan(0);
      expect(status?.parameters).toBeDefined();
    });

    it('should return null for non-existent strategy', () => {
      const status = engine.getStrategyStatus('NONEXISTENT');
      expect(status).toBeNull();
    });
  });

  describe('Signal Quality', () => {
    beforeEach(() => {
      // Add trending data for better signal quality
      const trendingData: OHLC[] = [];
      const basePrice = 1.0850;
      const now = new Date();
      
      for (let i = 0; i < 100; i++) {
        const timestamp = new Date(now.getTime() - (100 - i) * 60000);
        const trend = i * 0.0001; // Upward trend
        const open = basePrice + trend + (Math.random() - 0.5) * 0.001;
        const close = open + 0.0002; // Slight upward movement
        const high = Math.max(open, close) + Math.random() * 0.0005;
        const low = Math.min(open, close) - Math.random() * 0.0005;
        
        trendingData.push({
          timestamp,
          open,
          high,
          low,
          close,
          volume: Math.floor(Math.random() * 1000) + 100
        });
      }

      trendingData.forEach(candle => {
        engine.updateMarketData(candle);
      });
      engine.setActive(true);
    });

    it('should generate higher confidence signals with trending data', () => {
      const signal = engine.generateSignals();
      
      // With trending data, we should get some signal (not neutral)
      expect(['buy', 'sell']).toContain(signal.action);
      expect(signal.confidence).toBeGreaterThan(0);
    });

    it('should have consistent signal timing', () => {
      const signal1 = engine.generateSignals();
      const signal2 = engine.generateSignals();
      
      // Signals generated close together should have similar timestamps
      const timeDiff = Math.abs(signal2.timestamp.getTime() - signal1.timestamp.getTime());
      expect(timeDiff).toBeLessThan(1000); // Less than 1 second
    });
  });

  describe('Performance', () => {
    it('should generate signals quickly', () => {
      // Add data
      mockOHLCData.forEach(candle => {
        engine.updateMarketData(candle);
      });
      engine.setActive(true);

      const startTime = performance.now();
      engine.generateSignals();
      const endTime = performance.now();
      
      const executionTime = endTime - startTime;
      expect(executionTime).toBeLessThan(100); // Should complete in less than 100ms
    });

    it('should handle rapid data updates', () => {
      engine.setActive(true);
      
      const startTime = performance.now();
      for (let i = 0; i < 50; i++) {
        engine.updateMarketData(mockOHLCData[i]);
      }
      const endTime = performance.now();
      
      const executionTime = endTime - startTime;
      expect(executionTime).toBeLessThan(50); // Should handle 50 updates in less than 50ms
      expect(engine.getMarketDataLength()).toBe(50);
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty market data gracefully', () => {
      const signal = engine.generateSignals();
      expect(signal.action).toBe('neutral');
      expect(signal.confidence).toBe(0);
    });

    it('should handle identical price data', () => {
      const flatPrice = 1.0850;
      for (let i = 0; i < 50; i++) {
        const timestamp = new Date(Date.now() - (50 - i) * 60000);
        engine.updateMarketData({
          timestamp,
          open: flatPrice,
          high: flatPrice,
          low: flatPrice,
          close: flatPrice,
          volume: 100
        });
      }
      
      engine.setActive(true);
      const signal = engine.generateSignals();
      
      // Should handle flat data without errors
      expect(signal).toBeDefined();
      expect(signal.action).toBeDefined();
    });

    it('should handle extreme price movements', () => {
      const basePrice = 1.0850;
      const extremeCandle: OHLC = {
        timestamp: new Date(),
        open: basePrice,
        high: basePrice * 2, // 100% increase
        low: basePrice * 0.5, // 50% decrease
        close: basePrice * 1.5,
        volume: 1000
      };
      
      engine.updateMarketData(extremeCandle);
      expect(engine.getCurrentPrice()).toBe(basePrice * 1.5);
    });
  });
});

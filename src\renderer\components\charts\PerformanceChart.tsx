// 📊 Elite Performance Chart Component
import { useState, useEffect, useRef } from 'react';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';
import { 
  TrendingUp, 
  TrendingDown, 
  BarChart3, 
  PieChart,
  Activity,
  DollarSign
} from 'lucide-react';

interface PerformanceData {
  timestamp: Date;
  balance: number;
  profitLoss: number;
  winRate: number;
  totalTrades: number;
  drawdown: number;
}

interface PerformanceChartProps {
  data: PerformanceData[];
  timeRange: '1H' | '4H' | '1D' | '1W' | '1M';
  onTimeRangeChange?: (range: '1H' | '4H' | '1D' | '1W' | '1M') => void;
  className?: string;
}

const PerformanceChart: React.FC<PerformanceChartProps> = ({
  data,
  timeRange,
  onTimeRangeChange,
  className = ''
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [chartType, setChartType] = useState<'balance' | 'pnl' | 'winrate' | 'drawdown'>('balance');
  const [showGrid, setShowGrid] = useState(true);

  const timeRanges = [
    { value: '1H', label: '1H' },
    { value: '4H', label: '4H' },
    { value: '1D', label: '1D' },
    { value: '1W', label: '1W' },
    { value: '1M', label: '1M' }
  ];

  useEffect(() => {
    drawChart();
  }, [data, chartType, showGrid]);

  const drawChart = () => {
    const canvas = canvasRef.current;
    if (!canvas || data.length === 0) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    const rect = canvas.getBoundingClientRect();
    canvas.width = rect.width * window.devicePixelRatio;
    canvas.height = rect.height * window.devicePixelRatio;
    ctx.scale(window.devicePixelRatio, window.devicePixelRatio);

    const width = rect.width;
    const height = rect.height;
    const padding = 50;
    const chartWidth = width - padding * 2;
    const chartHeight = height - padding * 2;

    // Clear canvas
    ctx.fillStyle = 'var(--color-surface)';
    ctx.fillRect(0, 0, width, height);

    // Get data values based on chart type
    const values = data.map(d => {
      switch (chartType) {
        case 'balance': return d.balance;
        case 'pnl': return d.profitLoss;
        case 'winrate': return d.winRate;
        case 'drawdown': return d.drawdown;
        default: return d.balance;
      }
    });

    const minValue = Math.min(...values);
    const maxValue = Math.max(...values);
    const valueRange = maxValue - minValue;

    if (valueRange === 0) return;

    // Draw grid
    if (showGrid) {
      drawGrid(ctx, width, height, padding);
    }

    // Draw axes
    drawAxes(ctx, width, height, padding);

    // Draw labels
    drawLabels(ctx, minValue, maxValue, width, height, padding);

    // Draw chart based on type
    switch (chartType) {
      case 'balance':
        drawBalanceChart(ctx, values, chartWidth, chartHeight, padding, minValue, valueRange);
        break;
      case 'pnl':
        drawPnLChart(ctx, values, chartWidth, chartHeight, padding, minValue, valueRange);
        break;
      case 'winrate':
        drawWinRateChart(ctx, values, chartWidth, chartHeight, padding, minValue, valueRange);
        break;
      case 'drawdown':
        drawDrawdownChart(ctx, values, chartWidth, chartHeight, padding, minValue, valueRange);
        break;
    }

    // Draw data points
    drawDataPoints(ctx, values, chartWidth, chartHeight, padding, minValue, valueRange);
  };

  const drawGrid = (ctx: CanvasRenderingContext2D, width: number, height: number, padding: number) => {
    ctx.strokeStyle = 'var(--color-border)';
    ctx.lineWidth = 1;
    ctx.setLineDash([2, 2]);

    // Horizontal lines
    for (let i = 1; i < 5; i++) {
      const y = padding + (height - padding * 2) * (i / 5);
      ctx.beginPath();
      ctx.moveTo(padding, y);
      ctx.lineTo(width - padding, y);
      ctx.stroke();
    }

    // Vertical lines
    for (let i = 1; i < 6; i++) {
      const x = padding + (width - padding * 2) * (i / 6);
      ctx.beginPath();
      ctx.moveTo(x, padding);
      ctx.lineTo(x, height - padding);
      ctx.stroke();
    }

    ctx.setLineDash([]);
  };

  const drawAxes = (ctx: CanvasRenderingContext2D, width: number, height: number, padding: number) => {
    ctx.strokeStyle = 'var(--color-text-muted)';
    ctx.lineWidth = 2;

    // Y-axis
    ctx.beginPath();
    ctx.moveTo(padding, padding);
    ctx.lineTo(padding, height - padding);
    ctx.stroke();

    // X-axis
    ctx.beginPath();
    ctx.moveTo(padding, height - padding);
    ctx.lineTo(width - padding, height - padding);
    ctx.stroke();
  };

  const drawLabels = (ctx: CanvasRenderingContext2D, minValue: number, maxValue: number, width: number, height: number, padding: number) => {
    ctx.fillStyle = 'var(--color-text-muted)';
    ctx.font = '12px -apple-system, BlinkMacSystemFont, sans-serif';

    // Y-axis labels
    ctx.textAlign = 'right';
    for (let i = 0; i <= 5; i++) {
      const value = minValue + (maxValue - minValue) * (1 - i / 5);
      const y = padding + (height - padding * 2) * (i / 5);
      
      let label = '';
      switch (chartType) {
        case 'balance':
        case 'pnl':
          label = `$${value.toFixed(0)}`;
          break;
        case 'winrate':
          label = `${value.toFixed(1)}%`;
          break;
        case 'drawdown':
          label = `${value.toFixed(1)}%`;
          break;
      }
      
      ctx.fillText(label, padding - 10, y + 4);
    }

    // X-axis labels (time)
    ctx.textAlign = 'center';
    const labelCount = 6;
    for (let i = 0; i < labelCount; i++) {
      const dataIndex = Math.floor((data.length - 1) * (i / (labelCount - 1)));
      if (dataIndex < data.length) {
        const point = data[dataIndex];
        const x = padding + (width - padding * 2) * (i / (labelCount - 1));
        const timeStr = point.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        ctx.fillText(timeStr, x, height - padding + 20);
      }
    }
  };

  const drawBalanceChart = (ctx: CanvasRenderingContext2D, values: number[], chartWidth: number, chartHeight: number, padding: number, minValue: number, valueRange: number) => {
    if (values.length < 2) return;

    // Create gradient
    const gradient = ctx.createLinearGradient(0, padding, 0, padding + chartHeight);
    gradient.addColorStop(0, 'rgba(46, 160, 67, 0.3)');
    gradient.addColorStop(1, 'rgba(46, 160, 67, 0.05)');

    // Draw area
    ctx.beginPath();
    values.forEach((value, index) => {
      const x = padding + (chartWidth * index) / (values.length - 1);
      const y = padding + chartHeight - ((value - minValue) / valueRange) * chartHeight;

      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });

    // Close area
    ctx.lineTo(padding + chartWidth, padding + chartHeight);
    ctx.lineTo(padding, padding + chartHeight);
    ctx.closePath();
    ctx.fillStyle = gradient;
    ctx.fill();

    // Draw line
    ctx.strokeStyle = 'var(--color-success)';
    ctx.lineWidth = 3;
    ctx.beginPath();
    values.forEach((value, index) => {
      const x = padding + (chartWidth * index) / (values.length - 1);
      const y = padding + chartHeight - ((value - minValue) / valueRange) * chartHeight;

      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });
    ctx.stroke();
  };

  const drawPnLChart = (ctx: CanvasRenderingContext2D, values: number[], chartWidth: number, chartHeight: number, padding: number, minValue: number, valueRange: number) => {
    const zeroY = padding + chartHeight - ((0 - minValue) / valueRange) * chartHeight;

    // Draw zero line
    ctx.strokeStyle = 'var(--color-text-muted)';
    ctx.lineWidth = 1;
    ctx.setLineDash([5, 5]);
    ctx.beginPath();
    ctx.moveTo(padding, zeroY);
    ctx.lineTo(padding + chartWidth, zeroY);
    ctx.stroke();
    ctx.setLineDash([]);

    // Draw bars
    const barWidth = chartWidth / values.length * 0.8;
    values.forEach((value, index) => {
      const x = padding + (chartWidth * index) / (values.length - 1);
      const y = padding + chartHeight - ((value - minValue) / valueRange) * chartHeight;
      const barHeight = Math.abs(y - zeroY);

      ctx.fillStyle = value >= 0 ? 'var(--color-success)' : 'var(--color-danger)';
      
      if (value >= 0) {
        ctx.fillRect(x - barWidth / 2, y, barWidth, barHeight);
      } else {
        ctx.fillRect(x - barWidth / 2, zeroY, barWidth, barHeight);
      }
    });
  };

  const drawWinRateChart = (ctx: CanvasRenderingContext2D, values: number[], chartWidth: number, chartHeight: number, padding: number, minValue: number, valueRange: number) => {
    // Draw line chart
    ctx.strokeStyle = 'var(--color-info)';
    ctx.lineWidth = 3;
    ctx.beginPath();

    values.forEach((value, index) => {
      const x = padding + (chartWidth * index) / (values.length - 1);
      const y = padding + chartHeight - ((value - minValue) / valueRange) * chartHeight;

      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });

    ctx.stroke();

    // Add 50% reference line
    const fiftyPercentY = padding + chartHeight - ((50 - minValue) / valueRange) * chartHeight;
    ctx.strokeStyle = 'var(--color-warning)';
    ctx.lineWidth = 2;
    ctx.setLineDash([10, 5]);
    ctx.beginPath();
    ctx.moveTo(padding, fiftyPercentY);
    ctx.lineTo(padding + chartWidth, fiftyPercentY);
    ctx.stroke();
    ctx.setLineDash([]);
  };

  const drawDrawdownChart = (ctx: CanvasRenderingContext2D, values: number[], chartWidth: number, chartHeight: number, padding: number, minValue: number, valueRange: number) => {
    // Create gradient (red for drawdown)
    const gradient = ctx.createLinearGradient(0, padding, 0, padding + chartHeight);
    gradient.addColorStop(0, 'rgba(248, 81, 73, 0.3)');
    gradient.addColorStop(1, 'rgba(248, 81, 73, 0.05)');

    // Draw area
    ctx.beginPath();
    values.forEach((value, index) => {
      const x = padding + (chartWidth * index) / (values.length - 1);
      const y = padding + chartHeight - ((value - minValue) / valueRange) * chartHeight;

      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });

    ctx.lineTo(padding + chartWidth, padding + chartHeight);
    ctx.lineTo(padding, padding + chartHeight);
    ctx.closePath();
    ctx.fillStyle = gradient;
    ctx.fill();

    // Draw line
    ctx.strokeStyle = 'var(--color-danger)';
    ctx.lineWidth = 3;
    ctx.beginPath();
    values.forEach((value, index) => {
      const x = padding + (chartWidth * index) / (values.length - 1);
      const y = padding + chartHeight - ((value - minValue) / valueRange) * chartHeight;

      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });
    ctx.stroke();
  };

  const drawDataPoints = (ctx: CanvasRenderingContext2D, values: number[], chartWidth: number, chartHeight: number, padding: number, minValue: number, valueRange: number) => {
    ctx.fillStyle = 'var(--color-primary)';
    
    values.forEach((value, index) => {
      const x = padding + (chartWidth * index) / (values.length - 1);
      const y = padding + chartHeight - ((value - minValue) / valueRange) * chartHeight;

      ctx.beginPath();
      ctx.arc(x, y, 4, 0, 2 * Math.PI);
      ctx.fill();
    });
  };

  const getChartTitle = () => {
    switch (chartType) {
      case 'balance': return '💰 Account Balance';
      case 'pnl': return '📈 Profit & Loss';
      case 'winrate': return '🎯 Win Rate';
      case 'drawdown': return '📉 Drawdown';
      default: return '📊 Performance';
    }
  };

  const getChartIcon = () => {
    switch (chartType) {
      case 'balance': return <DollarSign className="h-5 w-5" />;
      case 'pnl': return <TrendingUp className="h-5 w-5" />;
      case 'winrate': return <BarChart3 className="h-5 w-5" />;
      case 'drawdown': return <TrendingDown className="h-5 w-5" />;
      default: return <Activity className="h-5 w-5" />;
    }
  };

  return (
    <Card 
      variant="trading" 
      className={className}
      title={getChartTitle()}
      icon={getChartIcon()}
      actions={
        <div className="flex items-center gap-2">
          {/* Chart Type Selector */}
          <div className="flex gap-1">
            {[
              { value: 'balance', label: 'Balance' },
              { value: 'pnl', label: 'P&L' },
              { value: 'winrate', label: 'Win Rate' },
              { value: 'drawdown', label: 'Drawdown' }
            ].map(type => (
              <Button
                key={type.value}
                variant={chartType === type.value ? 'primary' : 'ghost'}
                size="sm"
                onClick={() => setChartType(type.value as any)}
                className="px-2 py-1 text-xs"
              >
                {type.label}
              </Button>
            ))}
          </div>

          {/* Time Range Selector */}
          <div className="flex gap-1">
            {timeRanges.map(range => (
              <Button
                key={range.value}
                variant={timeRange === range.value ? 'primary' : 'ghost'}
                size="sm"
                onClick={() => onTimeRangeChange?.(range.value as any)}
                className="px-2 py-1 text-xs"
              >
                {range.label}
              </Button>
            ))}
          </div>

          {/* Grid Toggle */}
          <Button
            variant={showGrid ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setShowGrid(!showGrid)}
            className="px-2 py-1 text-xs"
          >
            Grid
          </Button>
        </div>
      }
    >
      <div className="relative">
        <canvas
          ref={canvasRef}
          className="w-full h-80 border border-[var(--color-border)] rounded"
        />
        
        {data.length === 0 && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <Activity className="h-12 w-12 text-[var(--color-text-muted)] mx-auto mb-4" />
              <p className="text-[var(--color-text-muted)]">No performance data available</p>
            </div>
          </div>
        )}

        {/* Current Value Display */}
        {data.length > 0 && (
          <div className="absolute top-4 right-4 bg-[var(--color-surface)]/90 backdrop-blur-sm rounded p-3 border border-[var(--color-border)]">
            <div className="text-right">
              <div className="text-xs text-[var(--color-text-muted)]">Current</div>
              <div className="text-lg font-bold">
                {chartType === 'balance' || chartType === 'pnl' 
                  ? `$${data[data.length - 1]?.[chartType].toFixed(2)}` 
                  : `${data[data.length - 1]?.[chartType].toFixed(1)}%`
                }
              </div>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
};

export default PerformanceChart;

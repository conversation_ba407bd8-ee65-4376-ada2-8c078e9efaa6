// 🎯 Elite Trading IPC Handlers
import { ipc<PERSON>ain, BrowserWindow } from 'electron';
import DatabaseManager from '../../database/models';
import PocketOptionWebSocket, { WebSocketConfig, OpenOrderRequest } from '../services/websocket';
import { TradingConfig, TradingSession, TradeResult, AssetInfo } from '../../shared/interfaces/trading';

export class TradingHandlers {
  private db: DatabaseManager;
  private ws: PocketOptionWebSocket | null = null;
  private mainWindow: BrowserWindow | null = null;
  private currentSession: TradingSession | null = null;

  constructor(mainWindow: BrowserWindow) {
    this.mainWindow = mainWindow;
    this.db = new DatabaseManager();
    this.setupHandlers();
  }

  private setupHandlers(): void {
    // 🔌 WebSocket Connection Handlers
    ipcMain.handle('ws:connect', async (_, config: WebSocketConfig) => {
      try {
        if (this.ws) {
          this.ws.disconnect();
        }

        this.ws = new PocketOptionWebSocket(config);
        this.setupWebSocketEvents();
        
        await this.ws.connect();
        await this.ws.authenticate();
        
        return { success: true, message: 'Connected and authenticated successfully' };
      } catch (error) {
        console.error('❌ WebSocket connection failed:', error);
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('ws:disconnect', async () => {
      try {
        if (this.ws) {
          this.ws.disconnect();
          this.ws = null;
        }
        return { success: true };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('ws:status', () => {
      return {
        connected: this.ws?.connected || false,
        authenticated: this.ws?.authenticated || false
      };
    });

    // 📊 Trading Session Handlers
    ipcMain.handle('session:create', async (_, sessionData: Omit<TradingSession, 'id'>) => {
      try {
        const sessionId = await this.db.createSession(sessionData);
        this.currentSession = { ...sessionData, id: sessionId };
        
        this.sendToRenderer('session:created', this.currentSession);
        return { success: true, sessionId };
      } catch (error) {
        console.error('❌ Failed to create session:', error);
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('session:get-active', async () => {
      try {
        const session = await this.db.getActiveSession();
        this.currentSession = session;
        return { success: true, session };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('session:update', async (_, sessionId: string, updates: Partial<TradingSession>) => {
      try {
        await this.db.updateSession(sessionId, updates);
        
        if (this.currentSession && this.currentSession.id === sessionId) {
          this.currentSession = { ...this.currentSession, ...updates };
          this.sendToRenderer('session:updated', this.currentSession);
        }
        
        return { success: true };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('session:end', async (_, sessionId: string) => {
      try {
        const endTime = new Date();
        await this.db.updateSession(sessionId, { 
          endTime, 
          isActive: false,
          finalBalance: this.currentSession?.currentBalance 
        });
        
        this.currentSession = null;
        this.sendToRenderer('session:ended', { sessionId, endTime });
        return { success: true };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    // 📈 Trade Execution Handlers
    ipcMain.handle('trade:open', async (_, tradeData: {
      asset: string;
      action: 'call' | 'put';
      amount: number;
      duration: string;
      strategy: string;
    }) => {
      try {
        if (!this.ws || !this.ws.authenticated) {
          throw new Error('WebSocket not connected or authenticated');
        }

        if (!this.currentSession) {
          throw new Error('No active trading session');
        }

        // Create trade record
        const trade: Omit<TradeResult, 'id'> = {
          sessionId: this.currentSession.id,
          asset: tradeData.asset,
          action: tradeData.action.toUpperCase() as any,
          amount: tradeData.amount,
          entryTime: new Date(),
          entryPrice: 0, // Will be updated when order is confirmed
          strategy: tradeData.strategy,
          isDemo: true, // TODO: Make configurable
          status: 'pending'
        };

        const tradeId = await this.db.createTrade(trade);

        // Send order to WebSocket
        const orderRequest: OpenOrderRequest = {
          action: tradeData.action,
          amount: tradeData.amount,
          asset: tradeData.asset,
          isDemo: 1,
          optionType: 100,
          requestId: Date.now(),
          time: tradeData.duration as any
        };

        await this.ws.openOrder(orderRequest);

        return { success: true, tradeId };
      } catch (error) {
        console.error('❌ Failed to open trade:', error);
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('trade:close', async (_, tradeId: string) => {
      try {
        if (!this.ws || !this.ws.authenticated) {
          throw new Error('WebSocket not connected or authenticated');
        }

        await this.ws.closeOrder(tradeId);
        return { success: true };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('trade:get-by-session', async (_, sessionId: string) => {
      try {
        const trades = await this.db.getTradesBySession(sessionId);
        return { success: true, trades };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    // 📊 Market Data Handlers
    ipcMain.handle('market:request-assets', async () => {
      try {
        if (!this.ws || !this.ws.authenticated) {
          throw new Error('WebSocket not connected or authenticated');
        }

        await this.ws.requestAssets();
        return { success: true };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('market:request-history', async (_, asset: string, timeframe: string, count: number) => {
      try {
        if (!this.ws || !this.ws.authenticated) {
          throw new Error('WebSocket not connected or authenticated');
        }

        await this.ws.requestHistory(asset, timeframe, count);
        return { success: true };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('market:subscribe', async (_, asset: string) => {
      try {
        if (!this.ws || !this.ws.authenticated) {
          throw new Error('WebSocket not connected or authenticated');
        }

        await this.ws.subscribeToAsset(asset);
        return { success: true };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    // 🗄️ Database Handlers
    ipcMain.handle('db:query', async (_, query: string, params: any[] = []) => {
      try {
        // This is a generic query handler - use with caution in production
        return { success: true, message: 'Query executed' };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });
  }

  private setupWebSocketEvents(): void {
    if (!this.ws) return;

    // Connection events
    this.ws.on('connected', () => {
      this.sendToRenderer('ws:connected');
    });

    this.ws.on('disconnected', (reason) => {
      this.sendToRenderer('ws:disconnected', reason);
    });

    this.ws.on('authenticated', (data) => {
      this.sendToRenderer('ws:authenticated', data);
    });

    // Trading events
    this.ws.on('order-opened', (data) => {
      this.sendToRenderer('trade:order-opened', data);
      this.handleOrderOpened(data);
    });

    this.ws.on('order-closed', (data) => {
      this.sendToRenderer('trade:order-closed', data);
      this.handleOrderClosed(data);
    });

    this.ws.on('balance-updated', (data) => {
      this.sendToRenderer('account:balance-updated', data);
      this.handleBalanceUpdate(data);
    });

    // Market data events
    this.ws.on('price-update', (data) => {
      this.sendToRenderer('market:price-update', data);
    });

    this.ws.on('assets-updated', (data) => {
      this.sendToRenderer('market:assets-updated', data);
    });

    this.ws.on('history-loaded', (data) => {
      this.sendToRenderer('market:history-loaded', data);
    });

    this.ws.on('new-tick', (data) => {
      this.sendToRenderer('market:new-tick', data);
    });

    // Error events
    this.ws.on('error', (error) => {
      this.sendToRenderer('ws:error', error);
    });
  }

  private async handleOrderOpened(data: any): Promise<void> {
    try {
      // Update trade record with actual entry price and order ID
      // Implementation depends on the exact structure of the response
      console.log('📈 Order opened:', data);
    } catch (error) {
      console.error('❌ Error handling order opened:', error);
    }
  }

  private async handleOrderClosed(data: any): Promise<void> {
    try {
      // Update trade record with exit price and result
      console.log('📉 Order closed:', data);
    } catch (error) {
      console.error('❌ Error handling order closed:', error);
    }
  }

  private async handleBalanceUpdate(data: any): Promise<void> {
    try {
      if (this.currentSession) {
        await this.db.updateSession(this.currentSession.id, {
          currentBalance: data.balance || data.amount
        });
      }
    } catch (error) {
      console.error('❌ Error handling balance update:', error);
    }
  }

  private sendToRenderer(channel: string, data?: any): void {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send(channel, data);
    }
  }

  // Cleanup
  cleanup(): void {
    if (this.ws) {
      this.ws.disconnect();
    }
    this.db.close();
  }
}

export default TradingHandlers;

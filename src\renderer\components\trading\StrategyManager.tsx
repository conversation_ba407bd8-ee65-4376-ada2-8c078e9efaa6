// 🧠 Elite Strategy Manager Component
import { useState, useEffect } from 'react';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { 
  Brain, 
  TrendingUp, 
  TrendingDown, 
  Activity, 
  Settings, 
  BarChart3,
  Zap,
  Target,
  Eye,
  EyeOff
} from 'lucide-react';
import SignalGenerator from '../../services/SignalGenerator';
import { TradingStrategy } from '../../services/TradingEngine';

interface StrategyManagerProps {
  signalGenerator: SignalGenerator;
  onSignalGenerated?: (signal: any) => void;
}

interface StrategyPerformance {
  name: string;
  enabled: boolean;
  weight: number;
  signalCount: number;
  avgConfidence: number;
}

const StrategyManager: React.FC<StrategyManagerProps> = ({ 
  signalGenerator, 
  onSignalGenerated 
}) => {
  const [strategies, setStrategies] = useState<TradingStrategy[]>([]);
  const [performance, setPerformance] = useState<StrategyPerformance[]>([]);
  const [signalStats, setSignalStats] = useState({
    totalSignals: 0,
    winRate: 0,
    avgConfidence: 0,
    lastSignalTime: null as Date | null,
    activeStrategies: 0
  });
  const [engineStatus, setEngineStatus] = useState({
    active: false,
    dataPoints: 0,
    currentPrice: 0,
    strategies: 0,
    enabledStrategies: 0
  });

  useEffect(() => {
    // Initialize data
    updateStrategyData();
    updateStats();

    // Set up event listeners
    const handleSignal = (event: any) => {
      updateStats();
      onSignalGenerated?.(event);
    };

    const handleStrategyUpdate = () => {
      updateStrategyData();
      updateStats();
    };

    signalGenerator.on('signal', handleSignal);
    signalGenerator.on('strategy_update', handleStrategyUpdate);

    // Update stats periodically
    const statsInterval = setInterval(updateStats, 5000);

    return () => {
      signalGenerator.off('signal', handleSignal);
      signalGenerator.off('strategy_update', handleStrategyUpdate);
      clearInterval(statsInterval);
    };
  }, [signalGenerator, onSignalGenerated]);

  const updateStrategyData = () => {
    // Note: We'll need to add a method to get strategies from the trading engine
    // For now, we'll use mock data
    const mockStrategies: TradingStrategy[] = [
      { name: 'RSI', enabled: true, weight: 0.15, parameters: { period: 14 } },
      { name: 'MACD', enabled: true, weight: 0.15, parameters: { fastPeriod: 12 } },
      { name: 'Bollinger Bands', enabled: true, weight: 0.15, parameters: { period: 20 } },
      { name: 'Moving Average', enabled: true, weight: 0.12, parameters: { fastPeriod: 10 } },
      { name: 'Stochastic', enabled: true, weight: 0.12, parameters: { period: 14 } },
      { name: 'Williams %R', enabled: true, weight: 0.10, parameters: { period: 14 } },
      { name: 'ATR Volatility', enabled: true, weight: 0.08, parameters: { period: 14 } },
      { name: 'ORB', enabled: true, weight: 0.13, parameters: { rangePeriod: 30 } },
    ];

    setStrategies(mockStrategies);
    setPerformance(signalGenerator.getStrategyPerformance());
  };

  const updateStats = () => {
    setSignalStats(signalGenerator.getSignalStats());
    setEngineStatus(signalGenerator.getEngineStatus());
  };

  const handleToggleStrategy = (strategyName: string, enabled: boolean) => {
    if (enabled) {
      signalGenerator.enableStrategy(strategyName);
    } else {
      signalGenerator.disableStrategy(strategyName);
    }
  };

  const handleWeightChange = (strategyName: string, weight: number) => {
    signalGenerator.updateStrategyWeight(strategyName, weight / 100);
  };

  const generateManualSignal = () => {
    const signal = signalGenerator.generateManualSignal();
    if (signal) {
      onSignalGenerated?.({
        type: 'manual_signal',
        data: signal,
        timestamp: new Date()
      });
    }
  };

  const getSignalIcon = (signal: string) => {
    switch (signal) {
      case 'buy': return <TrendingUp className="h-4 w-4 text-[var(--color-success)]" />;
      case 'sell': return <TrendingDown className="h-4 w-4 text-[var(--color-danger)]" />;
      default: return <Activity className="h-4 w-4 text-[var(--color-text-muted)]" />;
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return 'text-[var(--color-success)]';
    if (confidence >= 60) return 'text-[var(--color-warning)]';
    if (confidence >= 40) return 'text-[var(--color-info)]';
    return 'text-[var(--color-text-muted)]';
  };

  return (
    <div className="space-y-4">
      {/* Engine Status */}
      <Card variant="trading" title="🧠 Trading Intelligence Engine" icon={<Brain className="h-5 w-5" />}>
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
          <div>
            <div className="text-[var(--color-text-muted)]">Status</div>
            <div className={`font-semibold ${engineStatus.active ? 'text-[var(--color-success)]' : 'text-[var(--color-danger)]'}`}>
              {engineStatus.active ? 'Active' : 'Inactive'}
            </div>
          </div>
          <div>
            <div className="text-[var(--color-text-muted)]">Data Points</div>
            <div className="font-semibold">{engineStatus.dataPoints}</div>
          </div>
          <div>
            <div className="text-[var(--color-text-muted)]">Current Price</div>
            <div className="font-semibold">${engineStatus.currentPrice.toFixed(4)}</div>
          </div>
          <div>
            <div className="text-[var(--color-text-muted)]">Active Strategies</div>
            <div className="font-semibold text-[var(--color-primary)]">
              {engineStatus.enabledStrategies}/{engineStatus.strategies}
            </div>
          </div>
          <div>
            <div className="text-[var(--color-text-muted)]">Total Signals</div>
            <div className="font-semibold">{signalStats.totalSignals}</div>
          </div>
        </div>
        
        <div className="mt-4 flex gap-2">
          <Button
            variant="primary"
            size="sm"
            onClick={generateManualSignal}
            icon={<Zap className="h-4 w-4" />}
            disabled={!engineStatus.active || engineStatus.dataPoints < 50}
          >
            Generate Signal
          </Button>
          <Button
            variant="secondary"
            size="sm"
            onClick={updateStats}
            icon={<Activity className="h-4 w-4" />}
          >
            Refresh Stats
          </Button>
        </div>
      </Card>

      {/* Signal Statistics */}
      <Card variant="success" title="📊 Signal Performance" icon={<BarChart3 className="h-5 w-5" />}>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <div className="text-[var(--color-text-muted)]">Win Rate</div>
            <div className={`text-lg font-semibold ${getConfidenceColor(signalStats.winRate)}`}>
              {signalStats.winRate.toFixed(1)}%
            </div>
          </div>
          <div>
            <div className="text-[var(--color-text-muted)]">Avg Confidence</div>
            <div className={`text-lg font-semibold ${getConfidenceColor(signalStats.avgConfidence)}`}>
              {signalStats.avgConfidence.toFixed(1)}%
            </div>
          </div>
          <div>
            <div className="text-[var(--color-text-muted)]">Last Signal</div>
            <div className="text-lg font-semibold">
              {signalStats.lastSignalTime 
                ? new Date(signalStats.lastSignalTime).toLocaleTimeString()
                : 'None'
              }
            </div>
          </div>
          <div>
            <div className="text-[var(--color-text-muted)]">Active Count</div>
            <div className="text-lg font-semibold text-[var(--color-primary)]">
              {signalStats.activeStrategies}
            </div>
          </div>
        </div>
      </Card>

      {/* Strategy List */}
      <Card variant="trading" title="⚙️ Strategy Configuration" icon={<Settings className="h-5 w-5" />}>
        <div className="space-y-3">
          {strategies.map((strategy, index) => {
            const perf = performance.find(p => p.name === strategy.name);
            
            return (
              <div 
                key={strategy.name}
                className="flex items-center justify-between p-3 rounded border border-[var(--color-border)] hover:border-[var(--color-primary)] transition-colors"
              >
                <div className="flex items-center gap-3">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleToggleStrategy(strategy.name, !strategy.enabled)}
                    className="h-8 w-8"
                  >
                    {strategy.enabled ? (
                      <Eye className="h-4 w-4 text-[var(--color-success)]" />
                    ) : (
                      <EyeOff className="h-4 w-4 text-[var(--color-text-muted)]" />
                    )}
                  </Button>
                  
                  <div>
                    <div className="font-medium">{strategy.name}</div>
                    <div className="text-xs text-[var(--color-text-muted)]">
                      Signals: {perf?.signalCount || 0} | 
                      Confidence: {perf?.avgConfidence.toFixed(1) || '0.0'}%
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <div className="text-sm text-[var(--color-text-muted)]">Weight:</div>
                  <Input
                    type="number"
                    value={(strategy.weight * 100).toFixed(0)}
                    onChange={(e) => handleWeightChange(strategy.name, Number(e.target.value))}
                    size="compact"
                    className="w-16"
                    min="0"
                    max="100"
                    suffix="%"
                  />
                </div>
              </div>
            );
          })}
        </div>
      </Card>

      {/* Recent Signals */}
      <Card variant="glass" title="🎯 Recent Signals" icon={<Target className="h-5 w-5" />}>
        <div className="space-y-2">
          {signalGenerator.getSignalHistory().slice(-5).reverse().map((signal, index) => (
            <div 
              key={index}
              className="flex items-center justify-between p-2 rounded bg-[var(--color-surface)] border border-[var(--color-border)]"
            >
              <div className="flex items-center gap-2">
                {getSignalIcon(signal.signal.action)}
                <span className="font-medium">{signal.signal.action.toUpperCase()}</span>
                <span className="text-sm text-[var(--color-text-muted)]">
                  {signal.signal.timestamp.toLocaleTimeString()}
                </span>
              </div>
              
              <div className="flex items-center gap-2">
                <span className={`text-sm font-medium ${getConfidenceColor(signal.signal.confidence)}`}>
                  {signal.signal.confidence.toFixed(1)}%
                </span>
                <div className={`w-2 h-2 rounded-full ${
                  signal.executed 
                    ? signal.result === 'win' 
                      ? 'bg-[var(--color-success)]'
                      : signal.result === 'loss'
                      ? 'bg-[var(--color-danger)]'
                      : 'bg-[var(--color-warning)]'
                    : 'bg-[var(--color-text-muted)]'
                }`} />
              </div>
            </div>
          ))}
          
          {signalGenerator.getSignalHistory().length === 0 && (
            <div className="text-center text-[var(--color-text-muted)] py-4">
              No signals generated yet
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};

export default StrategyManager;

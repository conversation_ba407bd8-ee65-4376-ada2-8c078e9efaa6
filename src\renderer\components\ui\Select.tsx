// 🎯 Elite Select Component
import React, { useState, useRef, useEffect } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { clsx } from 'clsx';
import { ChevronDown, Check } from 'lucide-react';

const selectVariants = cva(
  'flex w-full items-center justify-between rounded-md border bg-transparent text-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 cursor-pointer',
  {
    variants: {
      variant: {
        default: 'border-[var(--color-input-border)] bg-[var(--color-input-bg)] text-[var(--color-text-primary)] focus-visible:ring-[var(--color-primary)]',
        error: 'border-[var(--color-danger)] bg-[var(--color-input-bg)] text-[var(--color-text-primary)] focus-visible:ring-[var(--color-danger)]',
        success: 'border-[var(--color-success)] bg-[var(--color-input-bg)] text-[var(--color-text-primary)] focus-visible:ring-[var(--color-success)]'
      },
      size: {
        sm: 'h-8 px-2 text-xs',
        default: 'h-10 px-3 py-2',
        lg: 'h-12 px-4 text-base',
        compact: 'h-8 px-1 text-sm'
      }
    },
    defaultVariants: {
      variant: 'default',
      size: 'default'
    }
  }
);

export interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
  icon?: React.ReactNode;
}

export interface SelectProps extends VariantProps<typeof selectVariants> {
  options: SelectOption[];
  value?: string;
  placeholder?: string;
  label?: string;
  error?: string;
  success?: string;
  disabled?: boolean;
  className?: string;
  onChange?: (value: string) => void;
}

const Select = React.forwardRef<HTMLDivElement, SelectProps>(
  ({ 
    className, 
    variant, 
    size, 
    options, 
    value, 
    placeholder = 'Select an option...', 
    label, 
    error, 
    success, 
    disabled,
    onChange,
    ...props 
  }, ref) => {
    const [isOpen, setIsOpen] = useState(false);
    const [selectedValue, setSelectedValue] = useState(value || '');
    const selectRef = useRef<HTMLDivElement>(null);

    // Determine variant based on error/success state
    const finalVariant = error ? 'error' : success ? 'success' : variant;

    // Find selected option
    const selectedOption = options.find(option => option.value === selectedValue);

    // Close dropdown when clicking outside
    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
          setIsOpen(false);
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    // Handle option selection
    const handleSelect = (optionValue: string) => {
      setSelectedValue(optionValue);
      setIsOpen(false);
      onChange?.(optionValue);
    };

    // Handle keyboard navigation
    const handleKeyDown = (event: React.KeyboardEvent) => {
      if (disabled) return;

      switch (event.key) {
        case 'Enter':
        case ' ':
          event.preventDefault();
          setIsOpen(!isOpen);
          break;
        case 'Escape':
          setIsOpen(false);
          break;
        case 'ArrowDown':
          event.preventDefault();
          if (!isOpen) {
            setIsOpen(true);
          } else {
            // Navigate to next option
            const currentIndex = options.findIndex(opt => opt.value === selectedValue);
            const nextIndex = Math.min(currentIndex + 1, options.length - 1);
            if (nextIndex !== currentIndex) {
              handleSelect(options[nextIndex].value);
            }
          }
          break;
        case 'ArrowUp':
          event.preventDefault();
          if (isOpen) {
            // Navigate to previous option
            const currentIndex = options.findIndex(opt => opt.value === selectedValue);
            const prevIndex = Math.max(currentIndex - 1, 0);
            if (prevIndex !== currentIndex) {
              handleSelect(options[prevIndex].value);
            }
          }
          break;
      }
    };

    return (
      <div className="w-full" ref={selectRef}>
        {label && (
          <label className="text-sm font-medium text-[var(--color-text-primary)] mb-2 block">
            {label}
          </label>
        )}
        
        <div className="relative">
          <div
            ref={ref}
            className={clsx(selectVariants({ variant: finalVariant, size, className }))}
            onClick={() => !disabled && setIsOpen(!isOpen)}
            onKeyDown={handleKeyDown}
            tabIndex={disabled ? -1 : 0}
            role="combobox"
            aria-expanded={isOpen}
            aria-haspopup="listbox"
            {...props}
          >
            <div className="flex items-center gap-2">
              {selectedOption?.icon && (
                <span className="text-[var(--color-text-muted)]">
                  {selectedOption.icon}
                </span>
              )}
              <span className={selectedOption ? 'text-[var(--color-text-primary)]' : 'text-[var(--color-text-muted)]'}>
                {selectedOption?.label || placeholder}
              </span>
            </div>
            
            <ChevronDown 
              className={clsx(
                'h-4 w-4 text-[var(--color-text-muted)] transition-transform duration-200',
                isOpen && 'rotate-180'
              )} 
            />
          </div>
          
          {/* Dropdown */}
          {isOpen && (
            <div className="absolute top-full left-0 right-0 z-50 mt-1 max-h-60 overflow-auto rounded-md border border-[var(--color-border)] bg-[var(--color-surface)] shadow-lg animate-fade-in">
              {options.map((option) => (
                <div
                  key={option.value}
                  className={clsx(
                    'flex items-center gap-2 px-3 py-2 text-sm cursor-pointer transition-colors',
                    'hover:bg-[var(--color-border)] focus:bg-[var(--color-border)]',
                    option.disabled && 'opacity-50 cursor-not-allowed',
                    selectedValue === option.value && 'bg-[var(--color-primary)] text-white'
                  )}
                  onClick={() => !option.disabled && handleSelect(option.value)}
                  role="option"
                  aria-selected={selectedValue === option.value}
                >
                  {option.icon && (
                    <span className="text-current">
                      {option.icon}
                    </span>
                  )}
                  <span className="flex-1">{option.label}</span>
                  {selectedValue === option.value && (
                    <Check className="h-4 w-4" />
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
        
        {/* Error Message */}
        {error && (
          <p className="text-[var(--color-danger)] text-xs mt-1 animate-fade-in">
            {error}
          </p>
        )}
        
        {/* Success Message */}
        {success && (
          <p className="text-[var(--color-success)] text-xs mt-1 animate-fade-in">
            {success}
          </p>
        )}
      </div>
    );
  }
);

Select.displayName = 'Select';

export { Select, selectVariants };
